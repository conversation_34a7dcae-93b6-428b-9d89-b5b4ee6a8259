#!/usr/bin/env sh

echo "Running pre-commit checks..."

# Run the build command
echo "Building project..."
npm run build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "Build successful! Proceeding with commit..."
    
    # Run lint-staged for additional checks
    npx lint-staged
else
    echo "Build failed! Commit aborted."
    echo "Please fix the build errors before committing."
    exit 1
fi
