# Pre-Commit Hooks Setup

This project uses <PERSON><PERSON> to enforce code quality checks before commits and pushes.

## Hooks Installed

### Pre-Commit Hook (`.husky/pre-commit`)

- **Purpose**: Runs before every commit
- **Checks**:
  - `npm run build` - Ensures the project builds successfully
  - `npx lint-staged` - Runs <PERSON>SLint, <PERSON><PERSON><PERSON>, and Jest tests on staged files
- **Result**: Commit is only allowed if build succeeds

### Pre-Push Hook (`.husky/pre-push`)

- **Purpose**: Runs before every push to remote
- **Checks**:
  - `npm run build` - Ensures the project builds successfully
- **Result**: Push is only allowed if build succeeds

## How It Works

1. **Pre-Commit**: When you run `git commit`, the hook will:

   - Build the project using `npm run build`
   - If build fails, the commit is aborted
   - If build succeeds, lint-staged runs additional checks
   - Only then is the commit allowed

2. **Pre-Push**: When you run `git push`, the hook will:
   - Build the project using `npm run build`
   - If build fails, the push is aborted
   - If build succeeds, the push proceeds

## Benefits

- **Prevents broken code**: Code that doesn't build cannot be committed or pushed
- **Maintains code quality**: Automated checks ensure consistent code standards
- **Catches errors early**: Issues are found before they reach the remote repository
- **Team consistency**: All team members follow the same quality gates

## Troubleshooting

### Build Fails

If the build fails during commit/push:

1. Fix the build errors (usually ESLint or TypeScript issues)
2. Run `npm run build` locally to verify the fix
3. Try committing/pushing again

### Hook Not Working

If hooks aren't running:

1. Ensure Husky is installed: `npm list husky`
2. Check hook permissions: `ls -la .husky/`
3. Reinstall hooks: `npm run prepare`

### Bypassing Hooks (Emergency Only)

To bypass hooks in emergency situations:

```bash
git commit --no-verify -m "emergency fix"
git push --no-verify
```

⚠️ **Warning**: Only use this for true emergencies, not to avoid fixing build issues.

## Configuration

The hooks are configured in:

- `.husky/pre-commit` - Pre-commit hook script
- `.husky/pre-push` - Pre-push hook script
- `package.json` - Husky and lint-staged configuration

## Dependencies

- **Husky**: Git hooks manager
- **lint-staged**: Runs linters on staged files
- **ESLint**: JavaScript/TypeScript linting
- **Prettier**: Code formatting
- **Jest**: Testing framework
