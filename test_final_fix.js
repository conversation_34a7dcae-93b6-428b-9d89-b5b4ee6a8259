// Test the final regex fix for RPA links with extra words

function extractRpaLinks(body) {
  if (!body) return [];

  console.log('🔍 Starting RPA extraction...');
  console.log('📧 Email body length:', body.length);
  
  // Updated regex to handle:
  // 1. Line breaks between "click here" and URL
  // 2. Extra words after "click here" (like "click here fake" or "click here original")
  const regex = /click here[^<]*<([^>]+)>/gi;
  const rpaLinks = [];
  
  try {
    const matches = Array.from(body.matchAll(regex));
    
    console.log(`🔍 matchAll found ${matches.length} RPA links`);
    
    matches.forEach((match, index) => {
      const url = match[1];
      console.log(`📄 Processing match ${index + 1}:`, match[0]);
      console.log(`🔗 Extracted URL:`, url);
      
      const urlParts = url.split('/');
      const filename = urlParts[urlParts.length - 1].split('?')[0] || `Notification_${index + 1}.pdf`;
      
      console.log(`📄 Processing RPA link ${index + 1}: ${filename}`);

      rpaLinks.push({
        filename: filename,
        name: filename,
        size: null,
        s3Url: url,
        contentType: 'application/pdf',
      });
    });
  } catch (error) {
    console.error('❌ Error during RPA extraction:', error);
  }

  console.log(`✅ Successfully extracted ${rpaLinks.length} RPA document(s)`);
  return rpaLinks;
}

// Test with the actual problematic email body
const actualEmailBody = `https://www.google.com/

click here fake <https://www.google.com/>

click here original
<https://elite-assets-dev.s3.us-east-1.amazonaws.com/attachments/1/4b2a571ddfa517866afafe80fcf895b65626f82c5da7372f2810a06f4a10bf20/4b2a571ddfa517866afafe80fcf895b65626f82c5da7372f2810a06f4a10bf20_0_1754572190349_notification_1_1_.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAXPK5SCYOKLBJYCZP%2F20250807%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250807T130952Z&X-Amz-Expires=86400&X-Amz-Signature=a7715e58b965b9d59cc07ae051731c258def7decf91a40eec50d1cb056072aba&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject>`;

console.log('=== TESTING FINAL REGEX FIX ===\n');

const result = extractRpaLinks(actualEmailBody);

console.log('\n=== RESULTS ===');
console.log(`Found ${result.length} RPA documents`);
if (result.length > 0) {
  result.forEach((doc, index) => {
    console.log(`${index + 1}. ${doc.filename}`);
    console.log(`   URL: ${doc.s3Url.substring(0, 60)}...`);
  });
} else {
  console.log('❌ No documents found - still an issue');
}

// Test edge cases
console.log('\n=== TESTING EDGE CASES ===');

const testCases = [
  {
    name: 'Standard format',
    body: 'click here <https://example.com/doc.pdf>'
  },
  {
    name: 'With extra word',
    body: 'click here fake <https://example.com/doc.pdf>'
  },
  {
    name: 'With multiple extra words',
    body: 'click here to access original document <https://example.com/doc.pdf>'
  },
  {
    name: 'With line break',
    body: 'click here\n<https://example.com/doc.pdf>'
  },
  {
    name: 'With extra word and line break',
    body: 'click here original\n<https://example.com/doc.pdf>'
  },
  {
    name: 'Multiple mixed formats',
    body: 'click here <https://example.com/doc1.pdf> and click here fake <https://example.com/doc2.pdf> and click here original\n<https://example.com/doc3.pdf>'
  }
];

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. Testing: ${testCase.name}`);
  const testResult = extractRpaLinks(testCase.body);
  console.log(`   Result: ${testResult.length} document(s) found`);
  if (testResult.length > 0) {
    testResult.forEach((doc, docIndex) => {
      console.log(`     ${docIndex + 1}. ${doc.filename}`);
    });
  }
});

console.log('\n=== TEST COMPLETE ===');
