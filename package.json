{"name": "elite-square", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write .", "test": "jest --coverage", "prepare": "husky"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@aws-sdk/s3-request-presigner": "^3.812.0", "@heroicons/react": "^2.2.0", "@next/env": "^15.2.4", "@next/font": "^14.2.15", "@reduxjs/toolkit": "^2.6.1", "@types/react-datepicker": "^6.2.0", "axios": "^1.8.4", "column-resizer": "^1.4.0", "eslint-config-prettier": "^10.1.1", "husky": "^9.1.7", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.0", "lodash": "^4.17.21", "lucide-react": "^0.484.0", "next": "15.2.3", "prettier": "^3.5.3", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "recharts": "^2.15.1", "tailwind-scrollbar": "^4.0.2", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.4", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "eslint": "^8.57.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "node-mocks-http": "^1.16.2", "postcss": "^8.5.3", "tailwindcss": "^4.0.15", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write", "jest --findRelatedTests --passWithNoTests"], "*.{json,css,md}": ["prettier --write"]}}