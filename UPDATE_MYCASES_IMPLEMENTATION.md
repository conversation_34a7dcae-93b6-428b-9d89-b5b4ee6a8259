# Update MyCases Button Implementation

## Overview
This implementation handles the requirement to disable "Delete event" buttons when the "Update MyCases" button has been clicked and events have `eventStatus: 'Scheduled'`.

## Key Components Modified

### 1. CourtNotice.tsx
- **Added `eventStatus` property** to the `EventType` interface to support the 'Scheduled' status
- **Modified delete button logic** to disable when `isUpdateMyCase` is true AND event has `eventStatus: 'Scheduled'`

```typescript
disabled={isChildWorkflow || isDisabled || (isUpdateMyCase && event.eventStatus === 'Scheduled')}
```

### 2. WorkflowField.tsx
- **Added state management** for `isUpdateMyCase` to track when the button was clicked
- **Added useEffect hook** to check form data on component initialization for events with `eventStatus: 'Scheduled'`
- **Enhanced "Update MyCases" button handler** to:
  - Make API call to archive-work-flow endpoint
  - Set `isUpdateMyCase` to true
  - Update all events in form data to have `eventStatus: 'Scheduled'`
  - Trigger re-render of court notice fields

### 3. API Integration
- **Archive API call**: `PUT /workflow/archive-work-flow` with payload:
  ```json
  {
    "work_flow_execution_id": "workflow_id",
    "type": "updatecase"
  }
  ```

## Workflow

### When "Update MyCases" Button is Clicked:
1. API call is made to update the case
2. `isUpdateMyCase` state is set to `true`
3. All events in the form data are updated with `eventStatus: 'Scheduled'`
4. Court notice fields are re-rendered with updated data
5. Delete buttons become disabled for scheduled events

### On Page Refresh:
1. Component initializes and checks existing form data
2. If any events have `eventStatus: 'Scheduled'`, sets `isUpdateMyCase` to `true`
3. Delete buttons remain disabled for scheduled events

### Delete Button Behavior:
- **Enabled**: When event is not scheduled or Update MyCases hasn't been clicked
- **Disabled**: When `isUpdateMyCase` is true AND event has `eventStatus: 'Scheduled'`

## Data Flow

```
API Response (workflow-render) 
  ↓
Form Data (with eventStatus)
  ↓
WorkflowField (checks for scheduled events)
  ↓
CourtNotice (receives isUpdateMyCase prop)
  ↓
Delete Button (disabled based on conditions)
```

## Testing
1. Load a workflow with court notice events
2. Click "Update MyCases" button
3. Verify delete buttons are disabled for scheduled events
4. Refresh the page
5. Verify delete buttons remain disabled

## Notes
- The implementation preserves state across page refreshes by checking the API response data
- Only events with `eventStatus: 'Scheduled'` have their delete buttons disabled
- Other events remain editable unless disabled by other conditions (child workflow, general disabled state) 