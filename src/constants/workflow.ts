/**
 * Workflow Constants
 * Centralized constants for workflow functionality
 */

// Field types used in workflow forms
export const FIELD_TYPES = {
  TEXT: 'text',
  EMAIL: 'email',
  PHONE: 'tel',
  TEXTAREA: 'textarea',
  CHECKBOX: 'checkbox',
  SELECT: 'select',
  RADIO: 'radio',
  DATE: 'date',
  TIME: 'time',
  NUMBER: 'number',
  FULLNAME: 'fullName',
  CONTACT_TYPE: 'contactType',
  NOTES: 'notes',
  CALL: 'call',
  CONFLICT: 'conflict',
};

// Task statuses
export const TASK_STATUS = {
  ACTIVE: 'active',
  PENDING: 'pending',
  COMPLETED: 'completed',
  SKIPPED: 'skipped',
};

// Task actions
export const TASK_ACTIONS = {
  SKIP: 'skip',
  COMPLETE: 'complete',
  SAVE: 'save',
};


// Task actions
export const VALIDATIONS = {
  COURT_TYPE_REQUIRED: 'Court notice type is required',
  COURT_NOTICE_DATE: 'Court notice date is required',
  FILE_UPLOAD_VALIDATION: 'Please add the Court Notice Type and Court notice action/Start date before uploading a file',
};

