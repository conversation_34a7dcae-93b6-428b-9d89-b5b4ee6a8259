import { useRef, useCallback, RefCallback } from 'react';

interface UseInfiniteScrollParams {
  loading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

/**
 * Custom hook for implementing infinite scrolling
 * @param loading - Whether data is currently loading
 * @param hasMore - Whether there's more data to load
 * @param onLoadMore - Callback to load more data
 * @returns A callback ref to attach to the last element
 */
export function useInfiniteScroll<T extends HTMLElement>({
  loading,
  hasMore,
  onLoadMore,
}: UseInfiniteScrollParams): RefCallback<T> {
  const observer = useRef<IntersectionObserver | null>(null);

  const lastElementRef = useCallback(
    (node: T | null) => {
      if (loading) return;

      if (observer.current) observer.current.disconnect();

      observer.current = new IntersectionObserver(entries => {
        if (entries[0].isIntersecting && hasMore) {
          onLoadMore();
        }
      });

      if (node) observer.current.observe(node);
    },
    [loading, hasMore, onLoadMore]
  );

  return lastElementRef;
}
