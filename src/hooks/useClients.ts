import { useState, useEffect, useCallback, useMemo } from 'react';
import { Client, ClientListParams, ClientStatus } from '@/types/client';

// Mock data for clients based on the image
const mockClients: Client[] = [
  {
    id: '1',
    name: '<PERSON>',
    status: 'Active',
    type: 'Client',
    email: '<EMAIL>',
    mobile: '(*************',
    attorney: '<PERSON>',
    syncStatus: 'Synced',
  },
  {
    id: '2',
    name: '<PERSON>',
    status: 'Active',
    type: 'Client',
    email: 'blake<PERSON>@hotmail.com',
    mobile: '(*************',
    attorney: '<PERSON>',
    syncStatus: 'Synced',
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    status: 'Closed',
    type: 'Client',
    email: '<EMAIL>',
    mobile: '(*************',
    attorney: '<PERSON><PERSON>',
    syncStatus: 'Synced',
  },
  {
    id: '4',
    name: '<PERSON>',
    status: 'Closed',
    type: 'Client',
    email: 'j<PERSON><PERSON>@gmail.com',
    mobile: '(*************',
    attorney: '<PERSON><PERSON>',
    syncStatus: 'Synced',
  },
  {
    id: '5',
    name: 'Lucas Butler',
    status: 'Closed',
    type: 'Client',
    email: '<EMAIL>',
    mobile: '(*************',
    attorney: 'T. Fredrick',
    syncStatus: 'Synced',
  },
];

interface UseClientsOptions {
  initialLimit?: number;
  autoLoad?: boolean;
}

interface UseClientsReturn {
  clients: Client[];
  filteredClients: Client[];
  selectedClients: string[];
  loading: boolean;
  error: string | null;
  searchTerm: string;
  statusFilter: ClientStatus | null;
  totalClients: number;
  isAllSelected: boolean;
  
  // Actions
  setSearchTerm: (term: string) => void;
  setStatusFilter: (status: ClientStatus | null) => void;
  selectClient: (clientId: string) => void;
  selectAllClients: () => void;
  clearSelection: () => void;
  loadClients: (params?: ClientListParams) => Promise<void>;
  refreshClients: () => Promise<void>;
}

export const useClients = (options: UseClientsOptions = {}): UseClientsReturn => {
  const { initialLimit = 50, autoLoad = false } = options;

  // State
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<ClientStatus | null>(null);

  // Load clients function
  const loadClients = useCallback(async (params: ClientListParams = {}) => {
    try {
      setLoading(true);
      setError(null);
      console.log('loadClients',params);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // For now, use mock data
      setClients(mockClients);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load clients');
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh clients
  const refreshClients = useCallback(() => {
    return loadClients({ limit: initialLimit });
  }, [loadClients, initialLimit]);

  // Auto-load on mount if enabled
  useEffect(() => {
    if (autoLoad) {
      loadClients({ limit: initialLimit });
    }
  }, [autoLoad, loadClients, initialLimit]);

  // Filtered clients based on search and status
  const filteredClients = useMemo(() => {
    let filtered = [...clients];

    // Apply search filter
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(client =>
        client.name.toLowerCase().includes(search) ||
        client.email.toLowerCase().includes(search) ||
        client.attorney.toLowerCase().includes(search) ||
        client.mobile.includes(search)
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(client => client.status === statusFilter);
    }

    return filtered;
  }, [clients, searchTerm, statusFilter]);

  // Selection management
  const selectClient = useCallback((clientId: string) => {
    setSelectedClients(prev => {
      if (prev.includes(clientId)) {
        return prev.filter(id => id !== clientId);
      }
      return [...prev, clientId];
    });
  }, []);

  const selectAllClients = useCallback(() => {
    if (selectedClients.length === filteredClients.length) {
      // Deselect all
      setSelectedClients([]);
    } else {
      // Select all filtered clients
      setSelectedClients(filteredClients.map(client => client.id));
    }
  }, [selectedClients.length, filteredClients]);

  const clearSelection = useCallback(() => {
    setSelectedClients([]);
  }, []);

  // Computed values
  const isAllSelected = selectedClients.length > 0 && selectedClients.length === filteredClients.length;
  const totalClients = clients.length;

  return {
    clients,
    filteredClients,
    selectedClients,
    loading,
    error,
    searchTerm,
    statusFilter,
    totalClients,
    isAllSelected,
    
    // Actions
    setSearchTerm,
    setStatusFilter,
    selectClient,
    selectAllClients,
    clearSelection,
    loadClients,
    refreshClients,
  };
}; 