import { useEffect, useRef, useMemo } from 'react';
import ColumnResizer from 'column-resizer';

interface UseColumnResizerOptions {
  liveDrag?: boolean;
  draggingClass?: string;
  gripInnerHtml?: string;
  minWidth?: number;
  headerOnly?: boolean;
  hoverCursor?: string;
  dragCursor?: string;
  postbackSafe?: boolean;
  flush?: boolean;
  marginLeft?: string;
  marginRight?: string;
  disable?: boolean;
  partialRefresh?: boolean;
  removePadding?: boolean;
}

export const useColumnResizer = (
  tableId: string,
  options: UseColumnResizerOptions = {}
) => {
  const resizerRef = useRef<ColumnResizer | null>(null);
  const tableRef = useRef<HTMLTableElement | null>(null);

  const defaultOptions: UseColumnResizerOptions = useMemo(() => ({
    liveDrag: true,
    draggingClass: 'column-dragging',
    gripInnerHtml: '<div class="column-grip"></div>',
    minWidth: 50,
    headerOnly: true,
    hoverCursor: 'col-resize',
    dragCursor: 'col-resize',
    postbackSafe: true,
    flush: true,
    marginLeft: '0px',
    marginRight: '0px',
    disable: false,
    partialRefresh: true,
    removePadding: false,
    ...options,
  }), [options]);

  useEffect(() => {
    const table = document.getElementById(tableId) as HTMLTableElement;
    if (!table) return;

    tableRef.current = table;

    // Initialize the column resizer
    try {
      resizerRef.current = new ColumnResizer(table, defaultOptions);
    } catch (error) {
      console.error('Error initializing column resizer:', error);
    }

    // Cleanup function
    return () => {
      if (resizerRef.current) {
        try {
          resizerRef.current.reset({ disable: true });
        } catch (error) {
          console.error('Error cleaning up column resizer:', error);
        }
        resizerRef.current = null;
      }
    };
  }, [tableId, defaultOptions]);

  const updateResizer = (newOptions: UseColumnResizerOptions = {}) => {
    if (resizerRef.current && tableRef.current) {
      try {
        resizerRef.current.reset({ ...defaultOptions, ...newOptions });
      } catch (error) {
        console.error('Error updating column resizer:', error);
      }
    }
  };

  const disableResizer = () => {
    if (resizerRef.current) {
      try {
        resizerRef.current.reset({ disable: true });
      } catch (error) {
        console.error('Error disabling column resizer:', error);
      }
    }
  };

  const enableResizer = () => {
    if (resizerRef.current && tableRef.current) {
      try {
        resizerRef.current.reset({ ...defaultOptions, disable: false });
      } catch (error) {
        console.error('Error enabling column resizer:', error);
      }
    }
  };

  return {
    updateResizer,
    disableResizer,
    enableResizer,
    resizerInstance: resizerRef.current,
  };
};