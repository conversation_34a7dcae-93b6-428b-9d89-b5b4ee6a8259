import { useState, useEffect, useCallback, useMemo } from 'react';
import { Matter, MatterListParams, MatterStatus } from '@/types/matter';

// Mock data for matters based on the image
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const mockMatters: any[] = [
  {
    id: '1',
    matterId: '453665',
    matter: 'Child Custody',
    status: 'Open',
    contact: '<PERSON>',
    attorney: '<PERSON><PERSON>',
  },
  {
    id: '2',
    matterId: '765763',
    matter: 'Divorce',
    status: 'Open',
    contact: '<PERSON>',
    attorney: '<PERSON>',
  },
  {
    id: '3',
    matterId: '659454',
    matter: 'Divorce',
    status: 'Closed',
    contact: '<PERSON><PERSON>',
    attorney: '<PERSON>',
  },
  {
    id: '4',
    matterId: '544534',
    matter: 'Prenup',
    status: 'Open',
    contact: '<PERSON><PERSON>',
    attorney: '<PERSON>',
  },
  {
    id: '5',
    matterId: '323455',
    matter: 'Divorce',
    status: 'Open',
    contact: '<PERSON>',
    attorney: '<PERSON>',
  },
  {
    id: '6',
    matterId: '854854',
    matter: 'Adoption',
    status: 'Open',
    contact: '<PERSON>',
    attorney: '<PERSON>',
  },
  {
    id: '7',
    matterId: '834324',
    matter: 'Trust Account',
    status: 'Open',
    contact: 'Lucas Clark',
    attorney: 'Grace Lee',
  },
];

interface UseMattersOptions {
  initialLimit?: number;
  autoLoad?: boolean;
}

interface UseMattersReturn {
  matters: Matter[];
  filteredMatters: Matter[];
  selectedMatters: string[];
  loading: boolean;
  error: string | null;
  searchTerm: string;
  statusFilter: MatterStatus | null;
  totalMatters: number;
  isAllSelected: boolean;
  
  // Actions
  setSearchTerm: (term: string) => void;
  setStatusFilter: (status: MatterStatus | null) => void;
  selectMatter: (matterId: string) => void;
  selectAllMatters: () => void;
  clearSelection: () => void;
  loadMatters: (params?: MatterListParams) => Promise<void>;
  refreshMatters: () => Promise<void>;
}

export const useMatters = (options: UseMattersOptions = {}): UseMattersReturn => {
  const { initialLimit = 50, autoLoad = false } = options;

  // State
  const [matters, setMatters] = useState<Matter[]>([]);
  const [selectedMatters, setSelectedMatters] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<MatterStatus | null>(null);

  // Load matters function
  const loadMatters = useCallback(async (params: MatterListParams = {}) => {
    try {
      setLoading(true);
      setError(null);
      console.log('loadMatters',params);
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // For now, use mock data
      setMatters(mockMatters);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load matters');
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh matters
  const refreshMatters = useCallback(() => {
    return loadMatters({ limit: initialLimit });
  }, [loadMatters, initialLimit]);

  // Auto-load on mount if enabled
  useEffect(() => {
    if (autoLoad) {
      loadMatters({ limit: initialLimit });
    }
  }, [autoLoad, loadMatters, initialLimit]);

  // Filtered matters based on search and status
  const filteredMatters = useMemo(() => {
    let filtered = [...matters];

    // Apply search filter
    if (searchTerm.trim()) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(matter =>
        matter.matterId.toLowerCase().includes(search) ||
        matter.matter.toLowerCase().includes(search) ||
        matter.contact.toLowerCase().includes(search) ||
        matter.attorney.toLowerCase().includes(search)
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(matter => matter.status === statusFilter);
    }

    return filtered;
  }, [matters, searchTerm, statusFilter]);

  // Selection management
  const selectMatter = useCallback((matterId: string) => {
    setSelectedMatters(prev => {
      if (prev.includes(matterId)) {
        return prev.filter(id => id !== matterId);
      }
      return [...prev, matterId];
    });
  }, []);

  const selectAllMatters = useCallback(() => {
    if (selectedMatters.length === filteredMatters.length) {
      // Deselect all
      setSelectedMatters([]);
    } else {
      // Select all filtered matters
      setSelectedMatters(filteredMatters.map(matter => matter.id));
    }
  }, [selectedMatters.length, filteredMatters]);

  const clearSelection = useCallback(() => {
    setSelectedMatters([]);
  }, []);

  // Computed values
  const isAllSelected = selectedMatters.length > 0 && selectedMatters.length === filteredMatters.length;
  const totalMatters = matters.length;

  return {
    matters,
    filteredMatters,
    selectedMatters,
    loading,
    error,
    searchTerm,
    statusFilter,
    totalMatters,
    isAllSelected,
    
    // Actions
    setSearchTerm,
    setStatusFilter,
    selectMatter,
    selectAllMatters,
    clearSelection,
    loadMatters,
    refreshMatters,
  };
}; 