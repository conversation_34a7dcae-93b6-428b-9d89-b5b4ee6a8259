import { formatDate, formatCurrency, truncateString } from '@/utils/format';

describe('Format Utils', () => {
  describe('formatDate', () => {
    test('formats a date with default options', () => {
      const date = new Date(2023, 0, 15); // January 15, 2023
      const result = formatDate(date);

      // The exact format depends on the locale, but should include year, month, and day
      expect(result).toContain('2023');
      expect(result).toContain('January');
      expect(result).toContain('15');
    });

    test('formats a date with custom options', () => {
      const date = new Date(2023, 0, 15);
      const result = formatDate(date, {
        year: 'numeric',
        month: 'short',
        day: '2-digit',
      });

      // Should include year and day, but short month name
      expect(result).toContain('2023');
      expect(result).toContain('Jan');
      expect(result).toContain('15');
    });
  });

  describe('formatCurrency', () => {
    test('formats a number as USD currency by default', () => {
      const amount = 1234.56;
      const result = formatCurrency(amount);

      expect(result).toBe('$1,234.56');
    });

    test('formats a number as EUR currency', () => {
      const amount = 1234.56;
      const result = formatCurrency(amount, 'EUR');

      // The exact format may vary, but should include EUR currency symbol
      expect(result).toContain('1,234.56');
      expect(result).toMatch(/€|EUR/); // Either € symbol or EUR code
    });
  });

  describe('truncateString', () => {
    test('truncates a string if longer than maxLength', () => {
      const str = 'This is a long string that needs truncating';
      const result = truncateString(str, 10);

      expect(result).toBe('This is a ...');
      expect(result.length).toBe(13); // 10 chars + 3 dots
    });

    test('does not truncate a string if shorter than maxLength', () => {
      const str = 'Short';
      const result = truncateString(str, 10);

      expect(result).toBe('Short');
      expect(result.length).toBe(5);
    });

    test('handles edge case of maxLength = 0', () => {
      const str = 'Test';
      const result = truncateString(str, 0);

      expect(result).toBe('...');
    });
  });
});
