import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Button from '@/components/Button';

describe('Button Component', () => {
  test('renders button with correct text', () => {
    render(<Button>Click me</Button>);
    const buttonElement = screen.getByTestId('button');
    expect(buttonElement).toBeInTheDocument();
    expect(buttonElement).toHaveTextContent('Click me');
  });

  test('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);

    const buttonElement = screen.getByTestId('button');
    fireEvent.click(buttonElement);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies correct variant styles', () => {
    const { rerender } = render(<Button variant="primary">Primary</Button>);
    let buttonElement = screen.getByTestId('button');
    expect(buttonElement).toHaveClass('bg-blue-600');

    rerender(<Button variant="secondary">Secondary</Button>);
    buttonElement = screen.getByTestId('button');
    expect(buttonElement).toHaveClass('bg-gray-200');

    rerender(<Button variant="danger">Danger</Button>);
    buttonElement = screen.getByTestId('button');
    expect(buttonElement).toHaveClass('bg-red-600');
  });

  test('applies correct size styles', () => {
    const { rerender } = render(<Button size="small">Small</Button>);
    let buttonElement = screen.getByTestId('button');
    expect(buttonElement).toHaveClass('py-1 px-2 text-sm');

    rerender(<Button size="medium">Medium</Button>);
    buttonElement = screen.getByTestId('button');
    expect(buttonElement).toHaveClass('py-2 px-4 text-base');

    rerender(<Button size="large">Large</Button>);
    buttonElement = screen.getByTestId('button');
    expect(buttonElement).toHaveClass('py-3 px-6 text-lg');
  });

  test('disables button when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>);
    const buttonElement = screen.getByTestId('button');
    expect(buttonElement).toBeDisabled();
    expect(buttonElement).toHaveClass('opacity-50');
    expect(buttonElement).toHaveClass('cursor-not-allowed');
  });
});
