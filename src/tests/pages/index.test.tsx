import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import counterReducer from '@/features/counter/counterSlice';
import Home from '@/pages/index';

// Create a test store with the counter reducer
const createTestStore = () =>
  configureStore({
    reducer: {
      counter: counterReducer,
    },
  });

describe('Home Page', () => {
  it('renders the home page with counter', () => {
    const store = createTestStore();

    render(
      <Provider store={store}>
        <Home />
      </Provider>
    );

    // Check for heading
    expect(screen.getByText(/Elite Square Next.js Boilerplate/i)).toBeInTheDocument();

    // Check for counter elements
    expect(screen.getByText(/Redux Counter Example/i)).toBeInTheDocument();
    expect(screen.getByText('+')).toBeInTheDocument();
    expect(screen.getByText('-')).toBeInTheDocument();
    expect(screen.getByText('Add 5')).toBeInTheDocument();
    expect(screen.getByText('Add 10 Async')).toBeInTheDocument();

    // Counter value should start at 0 (initial state)
    expect(screen.getByText('0')).toBeInTheDocument();
  });
});
