import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import SignIn from '@/pages/signin';
import { useRouter } from 'next/router';
import axios from 'axios';
import { toast } from 'react-hot-toast';

// Mock the useRouter hook
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock axios
jest.mock('axios');

// Mock react-hot-toast
jest.mock('react-hot-toast', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
  Toaster: jest.fn().mockReturnValue(<div data-testid="mock-toaster" />),
}));

describe('SignIn Page', () => {
  const mockRouter = {
    push: jest.fn(),
  };

  beforeEach(() => {
    jest.resetAllMocks();
    // Setup useRouter mock
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    // Clear localStorage
    window.localStorage.clear();
  });

  it('renders the sign in page correctly', () => {
    render(<SignIn />);

    // Test logo, title and description
    expect(screen.getByAltText('FirmProfit Logo')).toBeInTheDocument();
    expect(screen.getByText('Sign in to FirmProfit')).toBeInTheDocument();
    expect(screen.getByText('Enter your credentials to login to your account')).toBeInTheDocument();

    // Test social login buttons
    expect(screen.getByTestId('google-signin')).toBeInTheDocument();
    expect(screen.getByTestId('microsoft-signin')).toBeInTheDocument();
    expect(screen.getByText('Sign in with Google')).toBeInTheDocument();
    expect(screen.getByText('Sign in with Microsoft')).toBeInTheDocument();

    // Test form elements
    expect(screen.getByLabelText(/Email\*/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password\*/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
    expect(screen.getByTestId('signin-button')).toBeInTheDocument();
    expect(screen.getByText('Forgot password?')).toBeInTheDocument();
    expect(screen.getByText(/Don't have an account\?/i)).toBeInTheDocument();
    expect(screen.getByText('Sign up')).toBeInTheDocument();
  });

  it('toggles password visibility when eye icon is clicked', async () => {
    const user = userEvent.setup();
    render(<SignIn />);

    const passwordInput = screen.getByTestId('password-input');
    const toggleButton = screen.getByTestId('toggle-password-visibility');

    // Initially password should be hidden
    expect(passwordInput).toHaveAttribute('type', 'password');

    // Click the toggle button
    await user.click(toggleButton);

    // Now password should be visible
    await waitFor(() => {
      expect(passwordInput).toHaveAttribute('type', 'text');
    });

    // Click again to hide
    await user.click(toggleButton);

    // Password should be hidden again
    await waitFor(() => {
      expect(passwordInput).toHaveAttribute('type', 'password');
    });
  });

  it('validates email and shows error message', async () => {
    const user = userEvent.setup();
    render(<SignIn />);

    const emailInput = screen.getByTestId('email-input');

    // Enter invalid email
    await user.type(emailInput, 'invalid-email');

    // Blur the input to trigger validation
    await user.tab();

    // Error message should appear
    expect(await screen.findByTestId('email-error')).toHaveTextContent(
      'Please enter a valid email address'
    );
  });

  it('validates password and shows error message', async () => {
    const user = userEvent.setup();
    render(<SignIn />);

    const passwordInput = screen.getByTestId('password-input');

    // Enter invalid password (too short)
    await user.type(passwordInput, 'pass');

    // Blur the input to trigger validation
    await user.tab();

    // Error message should appear
    expect(await screen.findByTestId('password-error')).toHaveTextContent(
      'Password must be at least 8 characters long'
    );
  });

  it('handles successful login without MFA', async () => {
    const user = userEvent.setup();

    const mockResponse = {
      data: {
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            isMFAEnable: false,
          },
        },
        access_token: 'test-token',
      },
      status: 200,
    };

    // Mock axios post to return success
    (axios.post as jest.Mock).mockResolvedValueOnce(mockResponse);

    render(<SignIn />);

    // Fill in form
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'Password1!');

    // Submit form
    await user.click(screen.getByTestId('signin-button'));

    // Check if API was called with correct params
    await waitFor(() => {
      expect(axios.post).toHaveBeenCalledWith('/auth/login', {
        email: '<EMAIL>',
        password: 'Password1!',
      });
    });

    // Check if success toast was shown
    expect(toast.success).toHaveBeenCalledWith('Login successful!');

    // Check if user was redirected
    expect(mockRouter.push).toHaveBeenCalledWith('/auth/verifymfa');

    // Check if user data was stored in localStorage
    expect(localStorage.getItem('user')).toBe(JSON.stringify(mockResponse.data.data.user));
    expect(localStorage.getItem('authToken')).toBe(mockResponse.data.access_token);
  });

  it('handles successful login with MFA required', async () => {
    const user = userEvent.setup();

    const mockResponse = {
      data: {
        data: {
          user: {
            id: '123',
            email: '<EMAIL>',
            isMFAEnable: true,
          },
        },
        access_token: 'test-token',
        mfaRequired: true,
        userId: '123',
      },
      status: 200,
    };

    // Mock axios post to return success with MFA required
    (axios.post as jest.Mock).mockResolvedValueOnce(mockResponse);

    render(<SignIn />);

    // Fill in form
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'Password1!');

    // Submit form
    await user.click(screen.getByTestId('signin-button'));

    // Check if user was redirected to MFA verification
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith({
        pathname: '/auth/verifymfa',
        query: { userId: '123' },
      });
    });
  });

  it('handles invalid credentials error', async () => {
    const user = userEvent.setup();

    // Mock axios post to return error
    const mockError = {
      response: {
        status: 401,
        data: {
          message: 'Invalid email or password',
        },
      },
    };

    (axios.post as jest.Mock).mockRejectedValueOnce(mockError);

    render(<SignIn />);

    // Fill in form
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'WrongPassword1!');

    // Submit form
    await user.click(screen.getByTestId('signin-button'));

    // Check if error message is displayed
    await waitFor(() => {
      expect(screen.getByTestId('general-error')).toHaveTextContent('Invalid email or password');
      expect(toast.error).toHaveBeenCalledWith('Invalid email or password');
    });
  });

  it('handles account locked error', async () => {
    const user = userEvent.setup();

    // Mock axios post to return locked account error
    const mockError = {
      response: {
        status: 409,
        data: {
          message: 'Account locked due to too many failed attempts',
        },
      },
    };

    (axios.post as jest.Mock).mockRejectedValueOnce(mockError);

    render(<SignIn />);

    // Fill in form
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'Password1!');

    // Submit form
    await user.click(screen.getByTestId('signin-button'));

    // Check if user was redirected to account locked page
    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith('/auth/accountlocked');
      expect(toast.error).toHaveBeenCalledWith(
        'Account locked. Please check your email for instructions.'
      );
    });
  });

  it('handles network error', async () => {
    const user = userEvent.setup();

    // Mock axios post to return network error
    const mockError = new Error('Network Error');
    (axios.post as jest.Mock).mockRejectedValueOnce(mockError);

    render(<SignIn />);

    // Fill in form
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'Password1!');

    // Submit form
    await user.click(screen.getByTestId('signin-button'));

    // Check if error message is displayed
    await waitFor(() => {
      expect(screen.getByTestId('general-error')).toHaveTextContent(
        'Network error. Please try again.'
      );
      expect(toast.error).toHaveBeenCalledWith('An unexpected error occurred. Please try again.');
    });
  });

  it('handles multiple failed login attempts and locks account', async () => {
    const user = userEvent.setup();

    // Mock axios post to return unauthorized error
    const mockError = {
      response: {
        status: 401,
        data: {
          message: 'Invalid email or password',
        },
      },
    };

    (axios.post as jest.Mock).mockRejectedValue(mockError);

    render(<SignIn />);

    // Try to login 5 times (MAX_LOGIN_ATTEMPTS)
    for (let i = 0; i < 5; i++) {
      // Clear form and fill in again
      await user.clear(screen.getByTestId('email-input'));
      await user.clear(screen.getByTestId('password-input'));
      await user.type(screen.getByTestId('email-input'), '<EMAIL>');
      await user.type(screen.getByTestId('password-input'), 'Password1!');

      // Submit form
      await user.click(screen.getByTestId('signin-button'));

      // Wait for API call to complete
      await waitFor(() => {
        expect(axios.post).toHaveBeenCalled();
      });

      // Reset axios mock for next iteration
      (axios.post as jest.Mock).mockClear();
    }

    // After 5 failed attempts, account should be locked
    await waitFor(() => {
      expect(screen.getByTestId('general-error')).toHaveTextContent(
        'Your account is temporarily locked due to multiple failed sign in attempts.'
      );
      expect(toast.error).toHaveBeenCalledWith(
        'Your account is temporarily locked due to multiple failed sign in attempts.'
      );
    });
  });

  it('navigates to sign up page when sign up link is clicked', async () => {
    const user = userEvent.setup();

    render(<SignIn />);

    const signUpLink = screen.getByTestId('signup-link');
    await user.click(signUpLink);

    // Check if link has correct href
    expect(signUpLink).toHaveAttribute('href', '/signup');
  });

  it('navigates to forgot password page when forgot password link is clicked', async () => {
    const user = userEvent.setup();

    render(<SignIn />);

    const forgotPasswordLink = screen.getByTestId('forgot-password-link');
    await user.click(forgotPasswordLink);

    // Check if link has correct href
    expect(forgotPasswordLink).toHaveAttribute('href', '/forgot-password');
  });

  it('handles responsive design elements', () => {
    const { container } = render(<SignIn />);

    // Check for the main container with responsive classes
    const mainContainer = container.querySelector('.flex.min-h-screen');
    expect(mainContainer).toBeInTheDocument();

    // Check for the blue background container (hidden on mobile, visible on md+)
    const blueBackground = container.querySelector(
      '.hidden.md\\:block.md\\:flex-1.bg-\\[\\#3F73F6\\]'
    );
    expect(blueBackground).toBeInTheDocument();
  });
});
