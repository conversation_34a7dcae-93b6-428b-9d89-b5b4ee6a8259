import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { Contact } from '@/types/contact';
import { Matter } from '@/types/matter';
import { Event } from '@/types/event';
import { ContactWorkflow } from '@/types/workflow';
import { File } from '@/types/file';
import { ContactService } from '@/services/contactService';
import { MatterService } from '@/services/matterService';
import { EventService } from '@/services/eventService';
import { WorkflowService } from '@/services/workflowService';
import { FileService } from '@/services/fileService';
import {
  DetailPageLayout,
  InformationCard,
  MattersTable,
  EventsTable,
  WorkflowsTable,
  FilesTable
} from '@/components/common/DetailPage';
import { contactFieldConfig } from '@/config/entityFieldConfigs';

const ContactDetailPage: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  
  // State management
  const [contact, setContact] = useState<Contact | null>(null);
  const [matters, setMatters] = useState<Matter[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [workflows, setWorkflows] = useState<ContactWorkflow[]>([]);
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('about');

  // Load data on mount
  useEffect(() => {
    if (id && typeof id === 'string') {
      loadData(id);
    }
  }, [id]);

  const loadData = async (contactId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      // Load contact, matters, events, workflows, and files in parallel
      const [contactData, mattersData, eventsData, workflowsData, filesData] = await Promise.all([
        ContactService.getContact(contactId),
        MatterService.getMattersByContact(contactId),
        EventService.getEventsByContact(contactId),
        WorkflowService.getWorkflowsByContact(contactId),
        FileService.getFilesByContact(contactId)
      ]);
      
      setContact(contactData);
      setMatters(mattersData);
      setEvents(eventsData);
      setWorkflows(workflowsData);
      setFiles(filesData);
    } catch (err) {
      setError('Failed to load contact details');
      console.error('Error loading contact:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleWorkflowClick = (workflow: ContactWorkflow) => {
    // Navigate to workflow detail page or handle workflow click
    console.log('Workflow clicked:', workflow);
    // You can add navigation logic here, e.g.:
    // router.push(`/workflowrun?taskId=${workflow.id}&work_flow_id=${workflow.id}`);
  };

  const handleFileClick = (file: File) => {
    // Handle file click (e.g., preview or download)
    console.log('File clicked:', file);
    // You can add file preview logic here
  };

  const handleFileDownload = async (file: File) => {
    try {
      // Handle file download
      console.log('Downloading file:', file.fileName);
      // You can add actual download logic here
      // const blob = await FileService.downloadFile(file.id);
      // const url = window.URL.createObjectURL(blob);
      // const a = document.createElement('a');
      // a.href = url;
      // a.download = file.fileName;
      // document.body.appendChild(a);
      // a.click();
      // window.URL.revokeObjectURL(url);
      // document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const renderTabContent = () => {
    if (!contact) return null;

    switch (activeTab) {
      case 'about':
        return (
          <InformationCard
            title="Contact Information"
            data={contact}
            fields={contactFieldConfig}
          />
        );
      
      case 'matters':
        return (
          <MattersTable 
            matters={matters}
            title="Matters"
            emptyMessage="No matters found for this contact."
          />
        );
      
      case 'events':
        return (
          <EventsTable 
            events={events}
            title="Events"
            emptyMessage="No events found for this contact."
          />
        );
      
      case 'notes':
        return (
          <div className="bg-white">
            <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">Notes</h3>
            <p className="text-[14px] text-[#2A2E34]">{contact.notes || 'No notes available.'}</p>
          </div>
        );

      case 'workflows':
        return (
          <WorkflowsTable 
            workflows={workflows}
            title="Workflows"
            emptyMessage="No workflows found for this contact."
            onWorkflowClick={handleWorkflowClick}
          />
        );

      case 'files':
        return (
          <FilesTable 
            files={files}
            title="Files"
            emptyMessage="No files found for this contact."
            onFileClick={handleFileClick}
            onDownload={handleFileDownload}
          />
        );
      
      default:
        return (
          <div className="bg-white">
            <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">
              {activeTab.charAt(0).toUpperCase() + activeTab.slice(1).replace('-', ' ')}
            </h3>
            <p className="text-[14px] text-[#5F6F84]">
              Content for {activeTab} will be implemented soon.
            </p>
          </div>
        );
    }
  };

  return (
    <>
      <Head>
        <title>{contact ? `${contact.name} - Contact Details` : 'Contact Details'} - FirmProfit</title>
        <meta name="description" content={`View details for ${contact?.name || 'contact'} in FirmProfit`} />
      </Head>
      <DetailPageLayout
        entity={contact}
        loading={loading}
        error={error}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        backUrl="/contacts"
        backLabel="Back to Contacts"
        entityName="Contact"
        statusField="status"
      >
        {renderTabContent()}
      </DetailPageLayout>
    </>
  );
};

export default ContactDetailPage; 