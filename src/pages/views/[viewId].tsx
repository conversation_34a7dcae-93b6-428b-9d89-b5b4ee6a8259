import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { Poppins } from 'next/font/google';
import Layout from '@/components/layout/Layout';
import WorkflowSidebar from '@/components/workflows/sidebar/WorkflowSidebar';
import { UserViewService, UserView } from '@/services/userViewService';
import WorkflowsAssigned from '@/components/workflows/WorkflowsAssigned';
import CourtNoticeFollowUp from '@/components/workflows/CourtNoticeFollowUp';
import CourtNoticeCompleted from '@/components/workflows/CourtNoticeCompleted';
import CourtNoticeArchived from '@/components/workflows/CourtNoticeArchived';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const SavedViewPage = () => {
  const router = useRouter();
  const { viewId } = router.query;
  const [userView, setUserView] = useState<UserView | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserView = async () => {
      if (!viewId || typeof viewId !== 'string') return;

      try {
        setLoading(true);
        setError(null);
        const response = await UserViewService.getUserViewById(viewId);
        console.log('Fetched user view:', response.data);
        console.log('Full view object:', response.data.view);
        console.log('View object keys:', Object.keys(response.data.view || {}));
        if (response.data.success) {
          console.log('User view type:', response.data.view.type);
          setUserView(response.data.view);
        } else {
          setError('Failed to load saved view');
        }
      } catch (error) {
        console.error('Error fetching user view:', error);
        setError('Failed to load saved view');
      } finally {
        setLoading(false);
      }
    };

    fetchUserView();
  }, [viewId]);

  const handleNameClick = () => {
    router.push(
      `workflowrun?taskId=680019e55da8779ce1424284&work_flow_id=68076174e1af8cdccbe00bfc`
    );
  };

  // Render the appropriate component based on the view type
  const renderViewComponent = () => {
    if (!userView) return null;

    console.log('Rendering component for view type:', userView.type);

    switch (userView.type) {
      case 'follow_up':
        console.log('Rendering CourtNoticeFollowUp component');
        return <CourtNoticeFollowUp savedView={userView} viewTitle={userView.name} />;
      case 'completed':
        console.log('Rendering CourtNoticeCompleted component');
        return <CourtNoticeCompleted savedView={userView} viewTitle={userView.name} />;
      case 'archive':
        console.log('Rendering CourtNoticeArchived component');
        return <CourtNoticeArchived savedView={userView} viewTitle={userView.name} />;
      case 'my_work_flow':
      default:
        console.log('Rendering WorkflowsAssigned component (default)');
        return (
          <WorkflowsAssigned
            _onNameClick={handleNameClick}
            savedView={userView}
            viewTitle={userView.name}
          />
        );
    }
  };

  if (loading) {
    return (
      <>
        <Head>
          <title>Loading View - FirmProfit</title>
        </Head>
        <Layout>
          <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
            <WorkflowSidebar className="flex-shrink-0" />
            <div className="flex-1 overflow-auto flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading saved view...</p>
              </div>
            </div>
          </div>
        </Layout>
      </>
    );
  }

  if (error || !userView) {
    return (
      <>
        <Head>
          <title>View Not Found - FirmProfit</title>
        </Head>
        <Layout>
          <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
            <WorkflowSidebar className="flex-shrink-0" />
            <div className="flex-1 overflow-auto flex items-center justify-center">
              <div className="text-center">
                <p className="text-red-600 mb-4">{error || 'View not found'}</p>
                <button
                  onClick={() => router.push('/views')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                >
                  Back to Views
                </button>
              </div>
            </div>
          </div>
        </Layout>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>{userView.name} - FirmProfit</title>
        <meta name="description" content={`View ${userView.name} in FirmProfit`} />
      </Head>
      <Layout>
        <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
          <WorkflowSidebar className="flex-shrink-0" />
          <div className="flex-1 overflow-auto">
            {/* Render the appropriate component based on view type */}
            {renderViewComponent()}
          </div>
        </div>
      </Layout>
    </>
  );
};

export default SavedViewPage;
