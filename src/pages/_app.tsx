import '@/styles/globals.css';
import type { AppProps } from 'next/app';
import { Provider } from 'react-redux';
import { store } from '@/redux/store';
import { Toaster, toast } from 'react-hot-toast';
import React from 'react';
import { Poppins } from 'next/font/google';
import Head from 'next/head';
import RouteGuard from '@/components/RouteGuard';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

// Custom toast with dismiss button
const customToast = {
  // Custom error toast
  error: (message: string) => {
    toast.dismiss();
    return toast.custom(
      t => (
        <div
          className={`${
            t.visible ? 'animate-enter' : 'animate-leave'
          } min-w-[300px] max-w-md bg-[#EF8B8B] text-[#2a2e34] shadow-md ${poppins.className} rounded-[12px] w-[375px] h-[72px] pointer-events-auto flex items-center justify-between`}
          style={{ padding: '12px 16px' }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-2">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="12" r="10" stroke="#2a2e34" strokeWidth="2" />
                <path d="M12 7V13" stroke="#2a2e34" strokeWidth="2" strokeLinecap="round" />
                <circle cx="12" cy="17" r="1" fill="#2a2e34" />
              </svg>
            </div>
            <div className="text-[14px] font-normal">{message}</div>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="flex-shrink-0 cursor-pointer hover:opacity-70 ml-3"
            aria-label="Close"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="#2a2e34"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      ),
      { duration: 3000, position: 'top-center' }
    );
  },

  // Custom success toast
  success: (message: string) => {
    toast.dismiss();
    return toast.custom(
      t => (
        <div
          className={`${
            t.visible ? 'animate-enter' : 'animate-leave'
          } min-w-[300px] max-w-md bg-[#DEF7EC] text-[#03543E] shadow-md rounded-[12px] pointer-events-auto flex items-center justify-between`}
          style={{ padding: '12px 16px' }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-2">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="12" cy="12" r="10" stroke="#03543E" strokeWidth="2" />
                <path d="M16.59 7.58L10 14.17l-2.59-2.58L6 13l4 4 8-8z" fill="#03543E" />
              </svg>
            </div>
            <div className="text-[14px] font-medium">{message}</div>
          </div>
          <button
            onClick={() => toast.dismiss(t.id)}
            className="flex-shrink-0 hover:opacity-70 ml-3"
            aria-label="Close"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="#03543E"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      ),
      { duration: 3000, position: 'top-center' }
    );
  },
};

// Replace the default toast with our custom toast
// This overrides the default toast methods
Object.assign(toast, customToast);

export default function App({ Component, pageProps }: AppProps) {
  return (
    <Provider store={store}>
      <Head>
        <title>FirmProfit</title>
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="icon" href="/favicon.png" type="image/png" />
      </Head>
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 3000,
        }}
      />
      <RouteGuard>
        <Component {...pageProps} />
      </RouteGuard>
    </Provider>
  );
}
