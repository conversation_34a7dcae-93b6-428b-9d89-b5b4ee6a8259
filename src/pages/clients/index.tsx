import React from 'react';
import Head from 'next/head';
import { DataTable, PageHeader, StatusBadge, Column } from '@/components/common';
import Layout from '@/components/layout/Layout';
import ContactsSidebar from '@/components/layout/ContactsSidebar';
import { Client, ClientStatus } from '@/types/client';
import { useClients } from '@/hooks/useClients';
import { Poppins } from 'next/font/google';
import { useRouter } from 'next/router';

// Font setup
const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

const ClientsPage: React.FC = () => {
  const router = useRouter();

  // Use the custom clients hook
  const {
    filteredClients,
    selectedClients,
    loading,
    error,
    searchTerm,
    setSearchTerm,
    selectClient,
    selectAllClients,
    isAllSelected,
  } = useClients({
    initialLimit: 50,
    autoLoad: true,
  });

  // Status badge mapping for client statuses
  const getStatusBadgeProps = (status: ClientStatus) => {
    return status;
  };

  // Enhanced sync status badge component
  const SyncStatusBadge = ({ syncStatus }: { syncStatus: string }) => {
    const getSyncStatusStyle = (status: string) => {
      const normalizedStatus = status?.toLowerCase();

      switch (normalizedStatus) {
        case 'synced':
          return {
            backgroundColor: '#8CF1BD', // Light green background
            color: '#2A2E34', // Dark green text
          };
        case 'manual':
          return {
            backgroundColor: '#97C7FF', // Light blue background
            color: '#2A2E34', // Dark blue text
          };
        case 'pending':
          return {
            backgroundColor: '#FEF3C7', // Light yellow background
            color: '#92400E', // Dark yellow/orange text
          };
        case 'error':
        case 'failed':
          return {
            backgroundColor: '#FEE2E2', // Light red background
            color: '#991B1B', // Dark red text
          };
        default:
          return {
            backgroundColor: '#F3F4F6', // Light gray background
            color: '#374151', // Dark gray text
          };
      }
    };

    const style = getSyncStatusStyle(syncStatus);

    return (
      <span
        className="inline-flex items-center justify-center w-[100px] px-3 py-1 rounded-full text-[12px] text-center"
        style={{
          backgroundColor: style.backgroundColor,
          color: style.color,
        }}
      >
        {syncStatus || 'Unknown'}
      </span>
    );
  };

  // Handle client name click
  const handleClientClick = (clientId: string) => {
    router.push(`/clients/${clientId}`);
  };

  // Define table columns
  const columns: Column[] = [
    {
      id: 'name',
      header: 'NAME',
      sortable: true,
      cell: (row: Client) => (
        <div 
          className="text-[14px] font-medium text-[#2A2E34] cursor-pointer hover:text-[#3F73F6] transition-colors"
          onClick={() => handleClientClick(row.id)}
        >
          {row.name}
        </div>
      ),
    },
    {
      id: 'status',
      header: 'STATUS',
      sortable: true,
      cell: (row: Client) => (
        <StatusBadge 
          status={getStatusBadgeProps(row.status)} 
          variant="filled"
        />
      ),
    },
    {
      id: 'type',
      header: 'TYPE',
      sortable: true,
      cell: (row: Client) => (
        <div className="text-[14px] text-[#5F6F84]">
          {row.type}
        </div>
      ),
    },
    {
      id: 'email',
      header: 'EMAIL',
      sortable: true,
      cell: (row: Client) => (
        <div className="text-[14px] text-[#5F6F84] cursor-pointer hover:text-[#3F73F6]">
          {row.email}
        </div>
      ),
    },
    {
      id: 'mobile',
      header: 'MOBILE',
      sortable: true,
      cell: (row: Client) => (
        <div className="text-[14px] text-[#5F6F84]">
          {row.mobile}
        </div>
      ),
    },
    {
      id: 'attorney',
      header: 'ATTORNEY',
      sortable: true,
      cell: (row: Client) => (
        <div className="text-[14px] text-[#5F6F84]">
          {row.attorney || '-'}
        </div>
      ),
    },
    {
      id: 'syncStatus',
      header: 'SYNC STATUS',
      sortable: false,
      cell: (row: Client) => (
        <SyncStatusBadge syncStatus={row.syncStatus || ''} />
      ),
    },
  ];

  // Event handlers
  const handleFilter = () => {
    // Implement filter functionality
    console.log('Filter clicked');
  };

  const handleColumns = () => {
    // Implement column selection functionality
    console.log('Columns clicked');
  };

  const handleSaveView = () => {
    // Implement save view functionality
    console.log('Save view clicked');
  };

  return (
    <>
      <Head>
        <title>Clients - FirmProfit</title>
        <meta name="description" content="Manage and view clients in FirmProfit" />
      </Head>
      <Layout>
        <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
          <ContactsSidebar />
          <div className="flex-1 overflow-auto">
            <div className="p-6">
              {/* Page Header */}
              <PageHeader
                title="Clients list"
                searchValue={searchTerm}
                onSearchChange={setSearchTerm}
                onFilter={handleFilter}
                onColumns={handleColumns}
                onSaveView={handleSaveView}
                showFilter={false}
                showColumns={false}
                showSaveView={false}
              />

              {/* Error Display */}
              {error && (
                <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-[12px] border border-red-200">
                  {error}
                </div>
              )}

              {/* Data Table */}
              <DataTable
                columns={columns}
                showCheckbox={false}
                data={filteredClients}
                selectedIds={selectedClients}
                onSelectRow={selectClient}
                onSelectAll={selectAllClients}
                isAllSelected={isAllSelected}
                className="mt-4"
                isLoading={loading}
                idField="id"
                rowClassName="hover:bg-gray-50 transition-colors"
              />

              {/* Results Summary */}
              {!loading && (
                <div className="mt-4 text-sm text-[#5F6F84]">
                  {/* Showing {filteredClients.length} of {totalClients} clients */}
                  {selectedClients.length > 0 && (
                    <span className="ml-2">
                      • {selectedClients.length} selected
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default ClientsPage; 