import React, { useState } from 'react';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import { ResizableDataTable, DataTable, Column } from '@/components/common';

// Sample data for demonstration
const sampleData = [
  {
    id: '1',
    workflowName: 'Court Notice Processing',
    assignee: '<PERSON>',
    status: 'In Progress',
    dueDate: '2025-08-20',
    priority: 'High',
    description: 'Process court notice for case #12345'
  },
  {
    id: '2',
    workflowName: 'Document Review',
    assignee: '<PERSON>',
    status: 'Completed',
    dueDate: '2025-08-15',
    priority: 'Medium',
    description: 'Review legal documents for client consultation'
  },
  {
    id: '3',
    workflowName: 'Client Meeting Prep',
    assignee: '<PERSON>',
    status: 'Pending',
    dueDate: '2025-08-25',
    priority: 'Low',
    description: 'Prepare materials for upcoming client meeting'
  },
  {
    id: '4',
    workflowName: 'Case Research',
    assignee: '<PERSON>',
    status: 'In Progress',
    dueDate: '2025-08-22',
    priority: 'High',
    description: 'Research precedents for ongoing litigation case'
  },
  {
    id: '5',
    workflowName: 'Contract Analysis',
    assignee: '<PERSON>',
    status: 'Review',
    dueDate: '2025-08-18',
    priority: 'Medium',
    description: 'Analyze contract terms and conditions'
  }
];

const ResizableTablesDemo: React.FC = () => {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [enableResize, setEnableResize] = useState(true);

  // Define columns for the demo table
  const columns: Column[] = [
    {
      id: 'workflowName',
      header: 'WORKFLOW NAME',
      sortable: true,
      width: '200px',
      cell: (row) => (
        <div className="font-medium text-[#2A2E34]">
          {row.workflowName}
        </div>
      ),
    },
    {
      id: 'assignee',
      header: 'ASSIGNEE',
      sortable: true,
      width: '150px',
      cell: (row) => (
        <div className="text-[#5F6F84]">
          {row.assignee}
        </div>
      ),
    },
    {
      id: 'status',
      header: 'STATUS',
      sortable: true,
      width: '120px',
      cell: (row) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          row.status === 'Completed' ? 'bg-green-100 text-green-800' :
          row.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
          row.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {row.status}
        </span>
      ),
    },
    {
      id: 'priority',
      header: 'PRIORITY',
      sortable: true,
      width: '100px',
      cell: (row) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          row.priority === 'High' ? 'bg-red-100 text-red-800' :
          row.priority === 'Medium' ? 'bg-orange-100 text-orange-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {row.priority}
        </span>
      ),
    },
    {
      id: 'dueDate',
      header: 'DUE DATE',
      sortable: true,
      width: '120px',
      cell: (row) => (
        <div className="text-[#5F6F84]">
          {new Date(row.dueDate).toLocaleDateString()}
        </div>
      ),
    },
    {
      id: 'description',
      header: 'DESCRIPTION',
      sortable: false,
      width: '300px',
      cell: (row) => (
        <div className="text-[#5F6F84] truncate" title={row.description}>
          {row.description}
        </div>
      ),
    },
  ];

  const handleSelectRow = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedIds(sampleData.map(item => item.id));
      setIsAllSelected(true);
    } else {
      setSelectedIds([]);
      setIsAllSelected(false);
    }
  };

  return (
    <>
      <Head>
        <title>Resizable Tables Demo - FirmProfit</title>
        <meta name="description" content="Demonstration of resizable table columns" />
      </Head>

      <Layout>
        <div className="p-6 space-y-8">
          <div>
            <h1 className="text-3xl font-bold text-[#2A2E34] mb-2">
              Resizable Tables Demo
            </h1>
            <p className="text-[#5F6F84] mb-6">
              Demonstration of column resizing functionality in data tables. 
              Hover over column borders in the header to resize columns.
            </p>

            {/* Controls */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium mb-3">Controls</h3>
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={enableResize}
                    onChange={(e) => setEnableResize(e.target.checked)}
                    className="mr-2"
                  />
                  Enable Column Resizing
                </label>
                <div className="text-sm text-gray-600">
                  Selected rows: {selectedIds.length}
                </div>
              </div>
            </div>
          </div>

          {/* Resizable DataTable */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b">
              <h2 className="text-xl font-semibold text-[#2A2E34]">
                Resizable DataTable
              </h2>
              <p className="text-[#5F6F84] text-sm mt-1">
                This table uses the ResizableDataTable component with column resizing enabled.
                Hover over column borders to see the resize cursor.
              </p>
            </div>
            
            <div className="p-4">
              <ResizableDataTable
                columns={columns}
                data={sampleData}
                selectedIds={selectedIds}
                onSelectRow={handleSelectRow}
                onSelectAll={handleSelectAll}
                isAllSelected={isAllSelected}
                enableResize={enableResize}
                minColumnWidth={80}
                resizerOptions={{
                  liveDrag: true,
                  draggingClass: 'column-dragging',
                  gripInnerHtml: '<div class="column-grip"></div>',
                }}
              />
            </div>
          </div>

          {/* Regular DataTable for comparison */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-4 border-b">
              <h2 className="text-xl font-semibold text-[#2A2E34]">
                Regular DataTable (for comparison)
              </h2>
              <p className="text-[#5F6F84] text-sm mt-1">
                This is the original DataTable component without resizing functionality.
              </p>
            </div>
            
            <div className="p-4">
              <DataTable
                columns={columns}
                data={sampleData}
                selectedIds={[]}
                onSelectRow={() => {}}
                onSelectAll={() => {}}
                isAllSelected={false}
                resizable={false}
              />
            </div>
          </div>

          {/* Usage Instructions */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              How to Use Column Resizing
            </h3>
            <ul className="space-y-2 text-blue-800">
              <li>• Hover over the right border of any column header to see the resize cursor</li>
              <li>• Click and drag to resize the column to your desired width</li>
              <li>• The minimum column width is set to 80px by default</li>
              <li>• Column widths are maintained during data updates</li>
              <li>• Use the toggle above to enable/disable resizing functionality</li>
            </ul>
          </div>

          {/* Implementation Guide */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              Implementation Guide
            </h3>
            <div className="space-y-4 text-gray-700">
              <div>
                <h4 className="font-medium">Using ResizableDataTable:</h4>
                <pre className="bg-white p-3 rounded mt-2 text-sm overflow-x-auto">
{`import { ResizableDataTable } from '@/components/common';

<ResizableDataTable
  columns={columns}
  data={data}
  enableResize={true}
  minColumnWidth={80}
  resizerOptions={{
    liveDrag: true,
    draggingClass: 'column-dragging'
  }}
/>`}
                </pre>
              </div>
              
              <div>
                <h4 className="font-medium">Using existing DataTable with resizing:</h4>
                <pre className="bg-white p-3 rounded mt-2 text-sm overflow-x-auto">
{`import { DataTable } from '@/components/common';

<DataTable
  columns={columns}
  data={data}
  resizable={true}
  minColumnWidth={50}
/>`}
                </pre>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </>
  );
};

export default ResizableTablesDemo;