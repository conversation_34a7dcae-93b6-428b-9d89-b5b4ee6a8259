import React from 'react';
import Head from 'next/head';
import { Poppins } from 'next/font/google';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const AccountLockedPage = () => {
  return (
    <>
      <Head>
        <title>Authentication - FirmProfit</title>
        <meta name="description" content="Authentication page for FirmProfit" />
      </Head>
      <div className={`flex min-h-screen bg-white font-poppins md:flex-row ${poppins.className}`}>
        {/* Right Section - Blue Background */}
        <div className="hidden md:block md:flex-1 bg-[#3F73F6]">
          {/* Visible only on md screens and above */}
        </div>
      </div>
    </>
  );
};

export default AccountLockedPage;
