import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { Poppins } from 'next/font/google';
import { DevicePhoneMobileIcon, QrCodeIcon } from '@heroicons/react/24/outline';
import axios from 'axios';
import { useRouter } from 'next/router';
import toast from 'react-hot-toast';
import { Button, Form, Input } from '@/components';
import Head from 'next/head';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const MultiFactorAuthPage = () => {
  // Simulated MFA status - in real app, this would come from backend/context
  const [isMFAEnabled, setIsMFAEnabled] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState(''); // In real app, fetch from backend
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isCodeValid, setIsCodeValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const user = localStorage.getItem('user');
    const parserUser = JSON.parse(`${user}`);
    setIsMFAEnabled(parserUser?.isMFAEnable);

    const enableMfa = async () => {
      try {
        const user = localStorage.getItem('user');
        const id = JSON.parse(`${user}`).id;
        const response = await axios.post(process.env.NEXT_PUBLIC_BASE_URL + '/auth/mfa/enable', {
          userId: id,
        });
        setQrCodeUrl(response.data.data.qrCode);
      } catch (error) {
        console.error('Error enabling MFA:', error);
        toast.error('Failed to generate QR Code. Please try again later.');
      }
    };

    if (!isMFAEnabled) {
      enableMfa();
    }

    return () => {
      // Cleanup function
    };
  }, []);

  const handleVerificationCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value.replace(/\D/g, ''); // Only allow digits
    setVerificationCode(input);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSubmitVerification = async (e: any) => {
    e.preventDefault();

    if (verificationCode.length !== 6) {
      toast.error('Please enter a verification code');
      return;
    }

    setIsLoading(true);

    try {
      const user = localStorage.getItem('user');
      if (!user) {
        throw new Error('User session not found. Please log in again.');
      }

      const parsedUser = JSON.parse(user);
      const id = parsedUser?.id;
      if (!id) {
        throw new Error('User ID not found. Please log in again.');
      }

      const endpoint = isMFAEnabled ? '/auth/mfa/validate' : '/auth/mfa/verify';

      await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}${endpoint}`, {
        userId: id,
        otp: Number(verificationCode),
      });

      setIsCodeValid(true);

      localStorage.setItem('Mfa', JSON.stringify(true));

      // Update user's MFA status in localStorage if enabling MFA
      if (!isMFAEnabled) {
        localStorage.setItem(
          'user',
          JSON.stringify({
            ...parsedUser,
            isMFAEnable: true,
          })
        );
      }

      router.push('/all-work-flow?type=new-court-notice');
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const data = error.response?.data;

        switch (status) {
          case 400:
            toast.error(data?.message || 'Invalid verification code. Please try again.');
            break;
          case 409:
            // toast.error(data?.message || 'Account locked due to multiple failed attempts.');
            router.push('/auth/accountlocked');
            return; // Return early to prevent further execution
          case 429:
            toast.error(data?.message || 'Too many attempts. Please try again later.');
            break;
          default:
            toast.error(data?.message || 'An error occurred during verification.');
            break;
        }
      } else {
        toast.error(error.message || 'An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const renderMFAContent = () => {
    if (!isMFAEnabled) {
      return (
        <div className="w-full max-w-md">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <QrCodeIcon className="h-12 w-12 text-[#3F73F6]" aria-hidden="true" />
            </div>
            <h2 className="text-[24px] font-medium text-[#25282C] mb-4">
              Set Up Authenticator App
            </h2>
            <p className="text-[#5F6F84] leading-[20px] font-normal text-[14px] mb-6">
              Scan the QR code with your authenticator app (Google Authenticator, Authy)
            </p>

            {/* QR Code Section */}
            <div className="flex justify-center mb-6">
              {qrCodeUrl ? (
                <Image
                  src={qrCodeUrl}
                  alt="MFA QR Code"
                  width={200}
                  height={200}
                  className="border-2 border-gray-200 rounded-lg"
                />
              ) : (
                <div className="w-[200px] h-[200px] bg-gray-200 flex items-center justify-center rounded-lg">
                  QR Code Loading...
                </div>
              )}
            </div>

            <Form
              onSubmit={handleSubmitVerification}
              className="space-y-4"
              data-testid="signin-form"
            >
              <Input
                id="verificationCode"
                type="text"
                placeholder="Enter 6-digit code"
                maxLength={6}
                value={verificationCode}
                onChange={handleVerificationCodeChange}
                className="mb-4"
              />
              <Button
                variant="primary"
                type="submit" // This ensures the form's `onSubmit` is triggered
                fullWidth
                isLoading={isLoading}
                data-testid="verification-button"
              >
                {isMFAEnabled ? 'Verify and Complete Setup' : 'Verify code'}
              </Button>
            </Form>
          </div>
        </div>
      );
    }

    // Non-MFA (Code Verification) Version
    return (
      <div className="w-full max-w-md">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <DevicePhoneMobileIcon className="h-12 w-12 text-[#3F73F6]" aria-hidden="true" />
          </div>
          <h2 className="text-2xl font-medium text-[#25282C] mb-4">Verify Authentication</h2>
          <p className="text-[#5F6F84] mb-6">
            Enter the 6-digit verification code from your authentication app.
          </p>

          <Form onSubmit={handleSubmitVerification} className="space-y-4" data-testid="signin-form">
            <Input
              id="verificationCode"
              type="text"
              placeholder="Enter 6-digit code"
              maxLength={6}
              value={verificationCode}
              onChange={handleVerificationCodeChange}
              className="mb-4"
            />
            <Button
              variant="primary"
              type="submit" // This ensures the form's `onSubmit` is triggered
              fullWidth
              isLoading={isLoading}
              data-testid="verification-button"
            >
              Verify code
            </Button>
          </Form>
        </div>
      </div>
    );
  };

  return (
    <>
      <Head>
        <title>Verify MFA - FirmProfit</title>
        <meta name="description" content="Verify MFA in FirmProfit" />
      </Head>
      <div className={`flex min-h-screen bg-white ${poppins.className} md:flex-row`}>
        {/* Left Section - Form */}
        <div className="flex flex-1 flex-col px-6 py-12 lg:px-8 justify-center items-center">
          <div className="absolute top-[40px] left-[40px]">
            <Image src="../logo.svg" alt="FirmProfit Logo" width={169} height={40} />
          </div>

          {renderMFAContent()}
        </div>

        {/* Right Section - Blue Background */}
        <div className="hidden md:block md:flex-1 bg-[#3F73F6]">
          {/* Visible only on md screens and above */}
        </div>
      </div>
    </>
  );
};

export default MultiFactorAuthPage;
