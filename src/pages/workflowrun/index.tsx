import React from 'react';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import WorkflowRun from '@/components/workflows/workflowRun';
import { Poppins } from 'next/font/google';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

/**
 * WorkflowRunPage
 * Page component for displaying the main workflow interface
 */
const WorkflowRunPage = () => {
  // const [selectedView, setSelectedView] = useState('assigned');

  return (
    <>
      <Head>
        <title>Workflow Run - FirmProfit</title>
        <meta name="description" content="Run and manage workflows in FirmProfit" />
      </Head>
      <Layout>
        <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
          {/* <WorkflowSidebar className="flex-shrink-0" /> */}
          <div className="flex-1">
            <WorkflowRun />
          </div>
        </div>
      </Layout>
    </>
  );
};

export default WorkflowRunPage;
