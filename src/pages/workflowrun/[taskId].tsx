import React from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import Layout from '@/components/layout/Layout';
import WorkflowRun from '@/components/workflows/workflowRun';
import {getWorkflows} from '@/services/workflowService';
import { Task } from '@/types/workflow';

interface TaskPageProps {
  taskId: string;
}

/**
 * TaskPage
 * Page component for displaying a specific task within a workflow
 */
const TaskPage = ({ taskId }: TaskPageProps) => {
  return (
    <>
      <Head>
        <title>Task {taskId} - FirmProfit</title>
        <meta name="description" content={`View and manage task ${taskId} in FirmProfit`} />
      </Head>
      <Layout>
        <div className="flex ml-0 h-full overflow-hidden">
          <div className="flex-1">
            <WorkflowRun initialTaskId={taskId} />
          </div>
        </div>
      </Layout>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async context => {
  const { taskId } = context.params || {};

  if (!taskId || typeof taskId !== 'string') {
    return {
      redirect: {
        destination: '/workflowrun',
        permanent: false,
      },
    };
  }

  try {
    // Fetch all workflows to validate that the taskId exists
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const workflows: any = await getWorkflows();

    // Check if the taskId exists in any workflow
    let validTaskId = false;
    for (const workflow of workflows) {
      if (workflow.tasks.some((task: Task) => task.id === taskId)) {
        validTaskId = true;
        break;
      }
    }

    // If taskId doesn't exist, redirect to the main workflow page
    if (!validTaskId) {
      return {
        redirect: {
          destination: '/workflowrun',
          permanent: false,
        },
      };
    }

    return {
      props: {
        taskId,
      },
    };
  } catch (error) {
    console.error('Error validating task ID:', error);

    // If there's an error, still return the taskId and let the client-side handle it
    return {
      props: {
        taskId,
      },
    };
  }
};

export default TaskPage;
