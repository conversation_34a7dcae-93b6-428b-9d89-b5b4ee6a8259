import React, { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/layout/Layout';
import WorkflowSidebar from '@/components/workflows/sidebar/WorkflowSidebar';
import { Poppins } from 'next/font/google';
import {
  DataTable,
  PageHeader,
  StatusBadge,
  // Avatar,
  Column,
  Pagination,
} from '@/components/common';
import Head from 'next/head';
import { workflowService } from '@/services/api';

import SaveViewModal from '@/components/common/SaveViewModal';
import { UserViewService } from '@/services/userViewService';
import { mapStatusVariant } from '@/types/courtNotice';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

interface TableViewField {
  visible: boolean;
  FieldName: string;
  searchable: boolean;
  sortable: boolean;
}

interface CourtNoticeItem {
  assigned_users: string;
  id: string;
  _id: string;
  workflowRun: string;
  started: string;
  due: string;
  status: 'On Track' | 'Delayed' | 'Completed' | 'DUE SOON' | 'OVERDUE';
  activity: string;
  assignee: string;
  notes?: string;
  work_flow_runner: string;
  start_date: string;
  attorney?: string;
  matter?: string;
  templates?: string;
  contact?: string;
  task_completed?: string;
  view?: string[];
  last_activity?: string;
  work_flow_name?: string;
  end_date?: string;
  last_task_id?: string;
  template_name?: string;
  isChild?: string;
}

// type StatusType = 'On Track' | 'Delayed' | 'Completed';

const CourtNoticePage = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0 });
  const [viewFields, setViewFields] = useState<TableViewField[]>([]);
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const [courtNotices, setCourtNotices] = useState<CourtNoticeItem[]>([]);

  const [selectedNotices, setSelectedNotices] = useState<string[]>([]);
  const [visibleColumnNames, setVisibleColumnNames] = useState<string[] | null>(null);
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);

  const handleSelectNotice = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedNotices(prev => [...prev, id]);
    } else {
      setSelectedNotices(prev => prev.filter(noticeId => noticeId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedNotices(courtNotices.map(notice => notice.id));
    } else {
      setSelectedNotices([]);
    }
  };
  const getInitials = (name: string): string => {
    return name
      ?.split(' ')
      ?.map(word => word[0])
      ?.join('')
      ?.toUpperCase()
      ?.slice(0, 2);
  };
  const handleFilter = () => {
    console.log('Filter button clicked');
  };

  const handleFilters = (
    filters: Array<{
      id: string;
      field: string;
      operator: string;
      value: string;
      selectedValues?: string[];
    }>
  ) => {
    console.log('Filters changed', filters);

    // Convert FilterRow format to backend format
    const backendFilters = filters
      .filter(f => f.field && f.operator && (f.value || f.selectedValues?.length))
      .map(f => {
        // Map frontend field names to backend field names
        const fieldMap: Record<string, string> = {
          Name: 'template_name',
          Assignee: 'assigned_users',
          Status: 'status',
          Contact: 'contact',
          Matter: 'matter',
          Templates: 'templates',
          'Due date': 'due_date',
          'Create date': 'created_date',
          Attorney: 'attorney',
        };

        // Map frontend operators to backend operators
        const operatorMap: Record<string, string> = {
          contains: 'contains',
          equals: 'equal',
          not_contains: 'not_contains',
          not_equals: 'not_equal',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[f.field] || f.field;
        const operator = operatorMap[f.operator] || f.operator;
        const value =
          f.selectedValues && f.selectedValues.length > 0
            ? f.selectedValues
            : f.value.split(', ').filter(v => v.trim());

        return {
          fieldName,
          filter: operator,
          value,
        };
      });

    setCurrentFilters(backendFilters);
  };

  const handleColumns = (selected?: string[]) => {
    setVisibleColumnNames(selected ?? null);
  };

  const handleSaveView = () => {
    setShowSaveViewModal(true);
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      // Get current filters and columns state
      const viewData = {
        name: viewName,
        type: 'my_work_flow', // Default type for this page
        userId: 1, // TODO: Get from auth context
        filters: {
          search: searchTerm,
          sortBy,
          sortOrder,
        },
        columns: columns.map(col => ({
          FieldName: col.header,
          searchable: true,
          sortable: col.sortable || false,
          visible: true,
        })),
        filter: currentFilters,
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        console.log('View saved successfully:', response.data.view);
        // Refresh the sidebar views
        const windowWithRefresh = window as unknown as { refreshSidebarViews?: () => void };
        if (windowWithRefresh.refreshSidebarViews) {
          windowWithRefresh.refreshSidebarViews();
        }
        // You could show a success toast here
      } else {
        console.error('Failed to save view:', response.data.message);
        // You could show an error toast here
      }
    } catch (error) {
      console.error('Error saving view:', error);
      // You could show an error toast here
    } finally {
      setSavingView(false);
      setShowSaveViewModal(false);
    }
  };

  const handleSort = (field: string, order: 'asc' | 'desc') => {
    setSortBy(field);
    setSortOrder(order);
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const handleItemsPerPageChange = (limit: number) => {
    setPagination({ page: 1, limit, total: pagination.total });
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  const handleRowClick = (rowData: CourtNoticeItem) => {
    router.push(`/workflowrun?taskId=${rowData?.last_task_id}&work_flow_id=${rowData?._id}`);
  };

  useEffect(() => {
    const storedUser = localStorage.getItem('user');
    const storedUserId = storedUser ? JSON.parse(storedUser).id : null;
    setUserId(storedUserId);
  }, []);

  useEffect(() => {
    const fetchAll = async () => {
      const type = router.query.type as string;
      try {
        setLoading(true);
        setError(null);
        const viewRes = await workflowService.workFlowData(
          pagination.page,
          pagination.limit,
          userId ?? undefined,
          'View_1',
          type
        );
        console.log('🚀 ~ fetchAll ~ viewRes:', viewRes);

        // Handle different possible response structures - API returns nested data
        const responseData =
          (
            viewRes as {
              data?: { data?: unknown; fields?: unknown[]; column?: unknown[]; total?: number };
            }
          )?.data?.data ||
          (viewRes as { data?: unknown })?.data ||
          viewRes;
        const fields = (responseData as { fields?: unknown[] })?.fields || [];
        const columns = (responseData as { column?: unknown[] })?.column || [];
        const total = (responseData as { total?: number })?.total || 0;

        // Map API column format to component format
        const mappedColumns = (
          columns as {
            fieldName: string;
            isShowing: boolean;
            isSorting: boolean;
            isFilter: boolean;
          }[]
        ).map(col => ({
          FieldName: col.fieldName,
          visible: col.isShowing,
          sortable: col.isSorting,
          searchable: col.isFilter,
        }));

        const visibleColumns = mappedColumns.filter((f: TableViewField) => f.visible);
        console.log(
          '🚀 ~ visible columns:',
          visibleColumns.map((c: TableViewField) => c.FieldName)
        );
        setViewFields(visibleColumns);

        const transformedData: CourtNoticeItem[] = (
          fields as {
            id: string;
            assigned_users: string;
            work_flow_runner: string;
            start_date: string;
            end_date: string;
            status: string;
            last_activity: string;
            notes?: string;
            attorney?: string;
            matter?: string;
            template_name?: string;
            client_name?: string;
            completed_tasks?: string;
            last_task_id?: string;
          }[]
        ).map(field => ({
          id: field.id,
          _id: field.id,
          assigned_users: field.assigned_users,
          workflowRun: field.work_flow_runner,
          started: field.start_date,
          due: field.end_date,
          status: (() => {
            if (field.status === 'ON_TRACK') return 'On Track';
            if (field.status === 'DUE_SOON') return 'DUE SOON';
            if (field.status === 'OVERDUE') return 'OVERDUE';
            return 'Completed';
          })(),
          activity: field.last_activity,
          assignee: field.assigned_users,
          notes: field.notes,
          work_flow_runner: field.work_flow_runner,
          start_date: field.start_date,
          attorney: field.attorney,
          matter: field.matter,
          templates: field.template_name,
          contact: field.client_name,
          task_completed: field.completed_tasks,
          view: [],
          last_activity: field.last_activity,
          work_flow_name: field.template_name,
          end_date: field.end_date,
          last_task_id: field.last_task_id,
        }));

        console.log('🚀 ~ transformed data:', transformedData.length, 'items');
        setCourtNotices(transformedData);
        setPagination(prev => ({ ...prev, total }));
      } catch (e) {
        setError(e instanceof Error ? e.message : 'Failed to load workflows');
      } finally {
        setLoading(false);
      }
    };

    fetchAll();
  }, [pagination.page, pagination.limit, userId, router.query.type]);

  const columns: Column[] = useMemo(() => {
    const toId = (label: string) => label.toLowerCase().replace(/\s+/g, '_');

    const normalize = (label: string) => {
      const l = label.trim().toLowerCase().replace(/[_#]/g, ' ').replace(/\s+/g, ' ');
      console.log(l, 'LLLLLL');
      if (l === 'workflow name' || l === 'name') return 'name';
      if (l === 'due' || l === 'due on') return 'due on';
      if (l === 'task completed' || l === 'tasks completed') return 'task completed';
      if (l === 'attorney' || l === 'attorney') return 'attorney';
      if (l === 'matter') return 'matter';
      return l;
    };

    const mapCell = (fieldName: string): Column['cell'] => {
      const lower = normalize(fieldName);
      console.log('🚀 ~ mapCell ~ lower:', lower);
      const CellComponent = (row: CourtNoticeItem) => {
        if (lower === 'name' || lower === 'workflow name')
          return (
            <span
              className="text-[14px] text-[#2A2E34] cursor-pointer"
              onClick={() => handleRowClick(row)}
            >
              {row.work_flow_name}
            </span>
          );
        if (lower === 'view')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.isChild}
            </span>
          );
        if (lower === 'started')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.started}
            </span>
          );
        if (lower === 'due on' || lower === 'due')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.due}
            </span>
          );
        if (
          lower === 'task completed' ||
          lower === '#tasks completed' ||
          lower === 'task_completed'
        )
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.task_completed}
            </span>
          );
        if (lower === 'status')
          return (
            <div onClick={() => handleRowClick(row)} className="cursor-pointer">
              <StatusBadge status={mapStatusVariant(row.status)} variant="fill" />
            </div>
          );
        if (lower === 'activity')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.activity}
            </span>
          );
        if (lower === 'templates')
          return (
            <span className="text-[14px] cursor-pointer" onClick={() => handleRowClick(row)}>
              {row.isChild}
            </span>
          );
        if (lower === 'contact') return <span className="text-[14px]">{row.template_name}</span>;

        if (lower === 'assignee') {
          const assignedUsers = row?.assignee
            ? row?.assignee
                ?.split(',')
                .map((s: string) => s.trim())
                .filter(Boolean)
            : [];
          const userCount = assignedUsers.length || 0;
          const firstUser = assignedUsers[0];

          // Show AI robot icon if no assignee
          if (userCount === 0 || !firstUser) {
            return (
              <div
                className="w-[32px] h-[32px] cursor-pointer flex items-center justify-center"
                onClick={() => handleRowClick(row)}
              >
                <img src="/assets/ai-robot-new-2.svg" alt="AI Robot" className="w-6 h-6" />
              </div>
            );
          }

          return (
            <div
              className="relative w-[32px] h-[32px] cursor-pointer"
              onClick={() => handleRowClick(row)}
            >
              <div className="w-[32px] h-[32px] rounded-full bg-[#5F6F84] text-white flex items-center justify-center font-semibold">
                {getInitials(firstUser)}
              </div>
              <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                {userCount}
              </span>
            </div>
          );
        }

        if (lower === 'notes') return <span className="text-[14px]">{row.notes}</span>;
        if (lower === 'matter') return <span className="text-[14px]">{row.matter}</span>;

        if (lower === 'attorney') return <span className="text-[14px]">{row.attorney}</span>;
        return <span className="text-[14px]"></span>;
      };
      CellComponent.displayName = `CellComponent_${fieldName}`;
      return CellComponent;
    };

    const built = viewFields.map(f => {
      const lower = f.FieldName.toLowerCase();
      const isTaskCompletedHeader =
        lower === 'task completed' || lower === '#tasks completed' || lower === 'task_completed';

      return {
        id: toId(f.FieldName),
        header: f.FieldName.trim(), // Remove leading/trailing spaces from header
        sortable: f.sortable,
        cell: mapCell(f.FieldName),
        className: isTaskCompletedHeader ? 'whitespace-nowrap min-w-[200px]' : undefined,
        sortField: toId(f.FieldName),
      };
    });

    console.log(
      '🚀 ~ built columns:',
      built.map(c => c.header)
    );

    if (visibleColumnNames && visibleColumnNames.length > 0) {
      const normalizedSelected = new Set(visibleColumnNames.map(normalize));
      const filtered = built.filter(
        col =>
          typeof col.header === 'string' && normalizedSelected.has(normalize(String(col.header)))
      );
      console.log(
        '🚀 ~ filtered columns:',
        filtered.map(c => c.header)
      );
      return filtered;
    }
    console.log(
      '🚀 ~ returning all built columns:',
      built.map(c => c.header)
    );
    return built;
  }, [viewFields, visibleColumnNames]);

  return (
    <Layout>
      <Head>
        <title>Court Notice - FirmProfit</title>
        <meta name="description" content="Manage court notices in FirmProfit" />
      </Head>
      <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
        <WorkflowSidebar className="flex-shrink-0" />
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            <PageHeader
              title="Court Notice"
              searchValue={searchTerm}
              onSearchChange={setSearchTerm}
              onFilter={handleFilter}
              onColumns={handleColumns}
              onSaveView={handleSaveView}
              savedFilters={currentFilters}
              onFiltersChange={handleFilters}
            />

            {loading && <div className="text-center py-4">Loading...</div>}
            {error && <div className="text-red-500 text-center py-4">Error: {error}</div>}
            {!loading && !error && courtNotices.length === 0 && (
              <div className="text-center py-4">No data available</div>
            )}
            <DataTable
              columns={columns}
              data={courtNotices}
              selectedIds={selectedNotices}
              onSelectRow={handleSelectNotice}
              onSelectAll={handleSelectAll}
              isAllSelected={
                selectedNotices.length === courtNotices.length && courtNotices.length > 0
              }
              currentSortBy={sortBy}
              currentSortOrder={sortOrder}
              onSort={handleSort}
              idField="id"
              className="mt-4"
            />
            {!loading && courtNotices.length > 0 && (
              <Pagination
                currentPage={pagination.page}
                totalPages={totalPages}
                totalItems={pagination.total}
                itemsPerPage={pagination.limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                className="mt-6"
              />
            )}
          </div>
        </div>
      </div>

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </Layout>
  );
};

export default CourtNoticePage;
