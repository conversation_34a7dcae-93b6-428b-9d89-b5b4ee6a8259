import { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';

export default function Signup() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
  };

  return (
    <>
      <Head>
        <title>Sign Up - FirmProfit</title>
      </Head>
      <div className="min-h-screen flex flex-col md:flex-row">
        {/* Left Section */}
        <div className="w-full md:w-1/2 p-6 md:p-12 flex flex-col items-center justify-center">
          <div className="w-full max-w-md">
            {/* Logo */}
            <div className="mb-8">
              <Image src="/logo.png" alt="FirmProfit Logo" width={120} height={40} priority />
            </div>

            <h1 className="text-2xl font-semibold mb-2">Welcome to FirmProfit</h1>
            <p className="text-gray-600 mb-8">
              You must sign in to join or use your existing account
            </p>

            {/* Social Login Buttons */}
            <button className="w-full mb-3 flex items-center justify-center gap-2 border rounded-lg py-2.5 hover:bg-gray-50 transition-colors">
              <Image src="/google-icon.png" alt="Google" width={20} height={20} />
              Sign in with Google
            </button>
            <button className="w-full mb-8 flex items-center justify-center gap-2 border rounded-lg py-2.5 hover:bg-gray-50 transition-colors">
              <Image src="/microsoft-icon.png" alt="Microsoft" width={20} height={20} />
              Sign in with Microsoft
            </button>

            <div className="flex items-center gap-4 mb-8">
              <div className="flex-1 h-px bg-gray-200"></div>
              <span className="text-gray-500">Or</span>
              <div className="flex-1 h-px bg-gray-200"></div>
            </div>

            {/* Sign Up Form */}
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label htmlFor="email" className="block text-sm mb-1">
                  Email*
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your email"
                  required
                />
              </div>

              <div className="mb-6">
                <label htmlFor="password" className="block text-sm mb-1">
                  Password*
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    value={password}
                    onChange={e => setPassword(e.target.value)}
                    className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Create a password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2"
                  >
                    {showPassword ? 'Hide' : 'Show'}
                  </button>
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-6">
                By signing up, you agree to our{' '}
                <Link href="/privacy-policy" className="text-blue-600 hover:underline">
                  Privacy Policy
                </Link>{' '}
                and{' '}
                <Link href="/terms" className="text-blue-600 hover:underline">
                  Terms of Use
                </Link>
              </p>

              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-2.5 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create an account
              </button>
            </form>

            <p className="mt-6 text-center text-sm">
              Already have an account?{' '}
              <Link href="/signin" className="text-blue-600 hover:underline">
                Sign In
              </Link>
            </p>
          </div>
        </div>

        {/* Right Section - Blue Background */}
        <div className="hidden md:block w-1/2 bg-blue-600"></div>
      </div>
    </>
  );
}
