import React from 'react';

export interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger' | 'link';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  isLoading?: boolean;
  fullWidth?: boolean;
  'data-testid'?: string;
}

/**
 * Reusable Button component with consistent styling
 */
const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  className = '',
  type = 'button',
  icon,
  iconPosition = 'right',
  isLoading = false,
  fullWidth = false,
  'data-testid': testId = 'button',
}) => {
  const baseStyles =
    'rounded-[12px] font-medium transition-colors focus:outline-none flex items-center justify-center';

  const variantStyles = {
    primary: 'bg-[#3F73F6] hover:bg-blue-500 text-white',
    secondary: 'bg-white border border-[#DCE2EB] text-[#5F6F84] hover:bg-gray-50',
    danger: 'bg-[#EF8B8B] hover:bg-red-400 text-white',
    link: 'bg-transparent text-[#3F73F6] hover:text-blue-700 p-0',
  };

  const sizeStyles = {
    small: 'py-[8px] px-[12px] text-[12px]',
    medium: 'h-[40px] py-[12px] px-[16px] text-[14px] leading-[20px]',
    large: 'py-[16px] px-[24px] text-[16px]',
  };

  const disabledStyles = disabled || isLoading ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer';
  const widthStyles = fullWidth ? 'w-full' : '';

  const buttonClasses = `${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${disabledStyles} ${widthStyles} ${className}`;

  const LoadingSpinner = () => (
    <svg
      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      ></circle>
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  );

  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled || isLoading}
      data-testid={testId}
    >
      {isLoading && <LoadingSpinner />}
      {!isLoading && icon && iconPosition === 'left' && <span className="mr-2">{icon}</span>}
      {children}
      {!isLoading && icon && iconPosition === 'right' && <span className="ml-2">{icon}</span>}
    </button>
  );
};

export default Button;
