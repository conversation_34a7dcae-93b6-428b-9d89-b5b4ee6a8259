import React from 'react';
import {
  processAttributes,
  createE<PERSON><PERSON><PERSON><PERSON>,
  isValidElement,
  isSelfClosingTag,
} from '@/utils/elementUtils';

/**
 * Element component that dynamically renders HTML elements based on JSON structure
 * @param {Object} props - Component props
 * @param {Object} props.element - The element configuration object
 * @returns {React.ReactElement} - The rendered element
 */
const Element = ({ element }) => {
  // Validate the element
  if (!isValidElement(element)) {
    console.warn('Invalid element configuration:', element);
    return null;
  }

  const { tagName, attributes = {}, children = [], content } = element;
  const isSelfClosing = isSelfClosingTag(tagName);

  // Create the element with its attributes
  const createElement = () => {
    // Process attributes
    const props = processAttributes(attributes);

    // Handle event handlers
    if (props.onSubmit) {
      props.onSubmit = createEventHandler(props.onSubmit);
    }

    // For self-closing tags, don't include children
    if (isSelfClosing) {
      return React.createElement(tagName, props);
    }

    // Create the element with its children
    return React.createElement(
      tagName,
      props,
      content || children.map((child, index) => <Element key={index} element={child} />)
    );
  };

  return createElement();
};

export default Element;
