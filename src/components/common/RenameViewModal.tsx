import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';

interface RenameViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (newName: string) => void;
  currentName: string;
  isLoading?: boolean;
}

const RenameViewModal: React.FC<RenameViewModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentName,
  isLoading = false,
}) => {
  const [viewName, setViewName] = useState(currentName);

  useEffect(() => {
    if (isOpen) {
      setViewName(currentName);
    }
  }, [isOpen, currentName]);

  const handleSave = () => {
    if (viewName.trim()) {
      onSave(viewName.trim());
    }
  };

  const handleCancel = () => {
    setViewName(currentName);
    onClose();
  };

  // const _handleKeyPress = (e: React.KeyboardEvent) => {
  //   if (e.key === 'Enter' && !isLoading && viewName.trim()) {
  //     handleSave();
  //   } else if (e.key === 'Escape') {
  //     handleCancel();
  //   }
  // };

  const handleInputKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !isLoading && viewName.trim()) {
      e.preventDefault();
      handleSave();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setViewName(e.target.value);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleCancel();
    }
  };

  if (!isOpen) return null;

  const modalElement = (
    <div
      className="fixed inset-0 flex items-center justify-center z-[9999]"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.4)' }}
      onClick={handleBackdropClick}
    >
      <div
        className="bg-white rounded-[16px] relative"
        style={{
          width: '540px',
          height: '254px',
          boxShadow: '0px 8px 32px 0px #0000001A',
        }}
        onClick={e => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-6 pt-6 pb-4">
          <h2 className="text-[18px] font-semibold text-[#2A2E34] leading-[28px]">Rename view</h2>
          <button
            onClick={handleCancel}
            className="text-[#9AA4B2] hover:text-[#5F6F84] transition-colors"
            disabled={isLoading}
          >
            <X size={20} />
          </button>
        </div>

        {/* Body */}
        <div className="px-6 pb-6">
          <div className="mb-6">
            <label htmlFor="viewName" className="block text-[14px] font-medium text-[#2A2E34] mb-2">
              Name
            </label>
            <input
              id="viewName"
              type="text"
              value={viewName}
              onChange={handleInputChange}
              onKeyPress={handleInputKeyPress}
              placeholder="Name your view"
              className="w-full h-[48px] px-4 border border-[#E5E7EB] rounded-[12px] text-[14px] text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-[#3F73F6] focus:border-[#3F73F6] transition-colors"
              disabled={isLoading}
              autoFocus
            />
          </div>

          {/* Footer Buttons */}
          <div className="flex items-center justify-between">
            <button
              onClick={handleCancel}
              className="px-6 py-3 text-[14px] font-medium text-[#5F6F84] bg-white border border-[#E5E7EB] rounded-[8px] hover:bg-[#F9FAFB] focus:outline-none focus:ring-2 focus:ring-[#3F73F6] focus:ring-offset-2 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isLoading || !viewName.trim()}
              className="px-6 py-3 text-[14px] font-medium text-white bg-[#3F73F6] border border-transparent rounded-[8px] hover:bg-[#2563EB] focus:outline-none focus:ring-2 focus:ring-[#3F73F6] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return typeof window !== 'undefined' ? createPortal(modalElement, document.body) : null;
};

export default RenameViewModal;
