import React, { useState, useRef, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface TimePickerSimpleProps {
  selectedTime: Date | null;
  onTimeChange: (time: Date | null) => void;
  onTimeSelected?: (time: Date) => Promise<void>;
  disabled?: boolean;
  className?: string;
}

const TimePickerSimple: React.FC<TimePickerSimpleProps> = ({
  selectedTime,
  onTimeChange,
  onTimeSelected,
  disabled = false,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const timePickerRef = useRef<HTMLDivElement>(null);

  // Format time for display
  const formatTime = (time: Date | null) => {
    if (!time) return '';
    return time.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  // Handle time change
  const handleTimeChange = async (time: Date | null) => {
    onTimeChange(time);
    
    if (time && onTimeSelected) {
      try {
        setIsLoading(true);
        setError(null);
        await onTimeSelected(time);
      } catch (error) {
        setError('Failed to update time');
        console.error('Error updating time:', error);
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    } else {
      setIsOpen(false);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (timePickerRef.current && !timePickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={timePickerRef} className={`relative ${className}`}>
      <div 
        className="flex items-center justify-between border border-gray-300 rounded-xl overflow-hidden"
      >
        <input 
          type="text"
          value={selectedTime ? formatTime(selectedTime) : ''}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          readOnly
          placeholder="--:-- --"
          className="px-4 py-3 text-left outline-none text-gray-600 w-28 text-sm cursor-pointer"
          disabled={disabled}
        />
        <div 
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className="px-4 py-3 cursor-pointer bg-gray-50 border-l border-gray-300"
        >
          <svg
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-gray-600"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-50 mt-1">
          <DatePicker
            selected={selectedTime}
            onChange={handleTimeChange}
            showTimeSelect
            showTimeSelectOnly
            timeIntervals={15}
            timeCaption="Time"
            dateFormat="h:mm aa"
            timeFormat="h:mm aa"
            inline
            onClickOutside={() => setIsOpen(false)}
          />
        </div>
      )}

      {isLoading && (
        <div className="absolute right-10 top-3">
          <div className="spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      
      {error && (
        <div className="absolute top-full left-0 right-0 mt-1 text-xs text-red-500 bg-red-50 p-1 rounded">
          {error}
        </div>
      )}
    </div>
  );
};

export default TimePickerSimple; 