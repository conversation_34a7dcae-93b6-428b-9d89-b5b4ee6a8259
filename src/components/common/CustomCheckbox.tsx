import React from 'react';

interface CustomCheckboxProps {
  id: string;
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  disabled?: boolean;
  borderColor?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  id,
  borderColor = '#5F6F84',
  checked,
  onChange,
  className = '',
  disabled = false,
}) => {
  return (
    <div className={`relative w-[18px] h-[18px] ${className}`}>
      <input
        type="checkbox"
        id={id}
        className={`peer appearance-none w-full h-full rounded-[5px] border-2 border-[${borderColor}] checked:bg-[#3F73F6] checked:border-[#3F73F6] focus:outline-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed`}
        onChange={onChange}
        checked={checked}
        disabled={disabled}
      />
      <svg
        className="absolute top-0 left-0 w-full h-full pointer-events-none hidden peer-checked:block"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 18 18"
        fill="none"
      >
        <rect x="1" y="1" width="16" height="16" rx="5" stroke="#3F73F6" strokeWidth="2" />
        <path
          d="M5 9L8 12L13 6"
          stroke="white"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default CustomCheckbox;
