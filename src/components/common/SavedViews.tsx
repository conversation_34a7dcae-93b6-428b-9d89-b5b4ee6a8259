import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronRight, MoreHorizontal, Edit2, Trash2 } from 'lucide-react';
import { UserView, UserViewService } from '@/services/userViewService';
import { toast } from 'react-hot-toast';

interface SavedViewsProps {
  onViewSelect?: (view: UserView) => void;
  className?: string;
}

interface ViewMenuProps {
  view: UserView;
  onRename: (view: UserView) => void;
  onDelete: (view: UserView) => void;
  isOpen: boolean;
  onToggle: () => void;
}

const ViewMenu: React.FC<ViewMenuProps> = ({ view, onRename, onDelete, isOpen, onToggle }) => {
  return (
    <div className="relative">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onToggle();
        }}
        className="p-1 rounded hover:bg-gray-200 opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <MoreHorizontal size={14} />
      </button>
      
      {isOpen && (
        <div className="absolute right-0 top-6 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onRename(view);
              onToggle();
            }}
            className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <Edit2 size={14} />
            Rename
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete(view);
              onToggle();
            }}
            className="flex items-center gap-2 w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50"
          >
            <Trash2 size={14} />
            Delete
          </button>
        </div>
      )}
    </div>
  );
};

const SavedViews: React.FC<SavedViewsProps> = ({ onViewSelect, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [views, setViews] = useState<UserView[]>([]);
  const [loading, setLoading] = useState(false);
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [renamingView, setRenamingView] = useState<UserView | null>(null);
  const [newName, setNewName] = useState('');

  useEffect(() => {
    fetchViews();
  }, []);

  const fetchViews = async () => {
    try {
      setLoading(true);
      const response = await UserViewService.getUserViews();
      if (response.data.success) {
        setViews(response.data.views);
      }
    } catch (error) {
      console.error('Error fetching views:', error);
      toast.error('Failed to load saved views');
    } finally {
      setLoading(false);
    }
  };

  const handleViewClick = (view: UserView) => {
    if (onViewSelect) {
      onViewSelect(view);
    }
  };

  const handleRename = (view: UserView) => {
    setRenamingView(view);
    setNewName(view.name);
  };

  const handleRenameSubmit = async () => {
    if (!renamingView || !newName.trim()) return;

    try {
      const response = await UserViewService.renameUserView({
        viewId: renamingView.id,
        name: newName.trim(),
      });

      if (response.data.success) {
        setViews(views.map(v => v.id === renamingView.id ? { ...v, name: newName.trim() } : v));
        toast.success('View renamed successfully');
      }
    } catch (error) {
      console.error('Error renaming view:', error);
      toast.error('Failed to rename view');
    } finally {
      setRenamingView(null);
      setNewName('');
    }
  };

  const handleRenameCancel = () => {
    setRenamingView(null);
    setNewName('');
  };

  const handleDelete = async (view: UserView) => {
    if (!confirm(`Are you sure you want to delete the view "${view.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await UserViewService.deleteUserView({ viewId: view.id });
      if (response.data.success) {
        setViews(views.filter(v => v.id !== view.id));
        toast.success('View deleted successfully');
      }
    } catch (error) {
      console.error('Error deleting view:', error);
      toast.error('Failed to delete view');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRenameSubmit();
    } else if (e.key === 'Escape') {
      handleRenameCancel();
    }
  };

  return (
    <div className={`${className}`}>
      <div
        className="flex items-center justify-between py-2 px-3 cursor-pointer hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          <span className="text-sm font-medium text-gray-700">MY VIEWS</span>
        </div>
      </div>

      {isExpanded && (
        <div className="ml-4">
          {loading ? (
            <div className="py-2 px-3 text-sm text-gray-500">Loading...</div>
          ) : views.length === 0 ? (
            <div className="py-2 px-3 text-sm text-gray-500">No saved views</div>
          ) : (
            views.map((view) => (
              <div
                key={view.id}
                className="group flex items-center justify-between py-2 px-3 cursor-pointer hover:bg-gray-50 rounded"
                onClick={() => handleViewClick(view)}
              >
                {renamingView?.id === view.id ? (
                  <input
                    type="text"
                    value={newName}
                    onChange={(e) => setNewName(e.target.value)}
                    onKeyPress={handleKeyPress}
                    onBlur={handleRenameSubmit}
                    className="text-sm text-gray-700 bg-transparent border-b border-gray-300 focus:outline-none focus:border-blue-500 flex-1 mr-2"
                    autoFocus
                    onClick={(e) => e.stopPropagation()}
                  />
                ) : (
                  <span className="text-sm text-gray-700 truncate flex-1">{view.name}</span>
                )}
                
                <ViewMenu
                  view={view}
                  onRename={handleRename}
                  onDelete={handleDelete}
                  isOpen={openMenuId === view.id}
                  onToggle={() => setOpenMenuId(openMenuId === view.id ? null : view.id)}
                />
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default SavedViews;
