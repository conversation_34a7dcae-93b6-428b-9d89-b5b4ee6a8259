import React, { ReactNode } from 'react';
import TableActions from './TableActions';

interface PageHeaderProps {
  title: string;
  searchValue: string;
  onSearchChange: (value: string) => void;
  onFilter?: () => void;
  onColumns?: (selected?: string[]) => void;
  onSaveView?: () => void;
  actions?: ReactNode;
  showFilter?: boolean;
  showColumns?: boolean;
  showSaveView?: boolean;
  disableSaveView?: boolean;
  className?: string;
  visibleColumns?: string[]; // Array of visible column names
  savedFilters?: Array<{
    fieldName: string;
    filter: string;
    value: string[];
  }>; // Array of saved filter configurations
  onFiltersChange?: (
    filters: Array<{
      id: string;
      field: string;
      operator: string;
      value: string;
      selectedValues?: string[];
    }>
  ) => void; // Callback when filters change
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  searchValue,
  onSearchChange,
  onFilter,
  onColumns,
  onSaveView,
  actions,
  showFilter = true,
  showColumns = true,
  showSaveView = true,
  disableSaveView = false,
  className = '',
  visibleColumns,
  savedFilters,
  onFiltersChange,
}) => {
  return (
    <div className={`flex justify-between items-center mb-6 ${className}`}>
      <h1 className="text-2xl font-medium text-[#2A2E34]">{title}</h1>
      <div className="flex items-center gap-2">
        <TableActions
          searchValue={searchValue}
          onSearchChange={onSearchChange}
          onFilter={onFilter}
          onColumns={onColumns}
          onSaveView={onSaveView}
          showFilter={showFilter}
          showColumns={showColumns}
          showSaveView={showSaveView}
          disableSaveView={disableSaveView}
          visibleColumns={visibleColumns}
          savedFilters={savedFilters}
          onFiltersChange={onFiltersChange}
        />
        {actions}
      </div>
    </div>
  );
};

export default PageHeader;
