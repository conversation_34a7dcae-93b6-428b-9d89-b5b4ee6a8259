import React, { ReactNode, useEffect, useId, useMemo } from 'react';
import TableCheckbox from './TableCheckbox';
import TableHeader from './TableHeader';
import { useColumnResizer } from '@/hooks/useColumnResizer';
import '@/styles/column-resizer.css';

export interface Column {
  id: string;
  header: string;
  sortable?: boolean;
  sortField?: string; // Field name for sorting - maps to API field
  width?: string;
  minWidth?: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cell: (row: any, index: number) => ReactNode;
  className?: string;
  work_flow_name?: string;
  start_date?: string;
  end_date?: string;
  latest_task_id?: string;
}

interface DataTableProps {
  columns: Column[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any[];
  selectedIds?: string[];
  onSelectRow?: (id: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  isAllSelected?: boolean;
  className?: string;
  idField?: string;
  rowClassName?: string;
  isLoading?: boolean;
  showHeader?: boolean;
  showCheckbox?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  lastItemRef?: (node: any) => void;
  resizable?: boolean;
  minColumnWidth?: number;
  // Sorting props
  currentSortBy?: string;
  currentSortOrder?: 'asc' | 'desc';
  onSort?: (field: string, order: 'asc' | 'desc') => void;
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  selectedIds = [],
  onSelectRow,
  onSelectAll,
  isAllSelected = false,
  className = '',
  idField = 'id',
  rowClassName = '',
  isLoading = false,
  lastItemRef,
  showCheckbox = true,
  resizable = true,
  minColumnWidth = 50,
  currentSortBy,
  currentSortOrder,
  onSort,
}) => {
  const tableId = useId();
  const uniqueTableId = `data-table-${tableId}`;

  const headerMinWidths = useMemo(() => {
    // SSR-safe guard: if no DOM, fall back to approximation
    const canMeasure = typeof document !== 'undefined' && typeof window !== 'undefined';
    if (!canMeasure) {
      const avgChar = 8;
      const letterSpacing = 0.7;
      const padding = 24;
      const buffer = 10;
      return columns.map(col => {
        const len = (col.header || '').length;
        const approx = Math.ceil(len * (avgChar + letterSpacing) + padding + buffer);
        return Math.max(minColumnWidth, col.minWidth ?? approx);
      });
    }
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      return columns.map(col => Math.max(minColumnWidth, (col.minWidth ?? 0)));
    }
    // Match TableHeader styles: 14px font, medium weight, Poppins if available
    ctx.font = '500 14px Poppins, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, "Noto Sans", "Liberation Sans", sans-serif';
    const letterSpacingPx = 0.7; // tracking-wider ~0.05em * 14px
    const paddingPx = 24; // px-3 left + right
    const safetyBufferPx = 10; // extra to avoid clipping due to rounding/uppercase
    return columns.map(col => {
      const label = col.header || '';
      const measured = ctx.measureText(label.toUpperCase());
      const spacingAdjustment = Math.max(0, label.length - 1) * letterSpacingPx;
      const computed = Math.ceil(measured.width + spacingAdjustment + paddingPx + safetyBufferPx);
      return Math.max(minColumnWidth, col.minWidth ?? computed);
    });
  }, [columns, minColumnWidth]);

  // Initialize column resizer
  const { disableResizer, enableResizer } = useColumnResizer(
    uniqueTableId,
    {
      liveDrag: true,
      draggingClass: 'column-dragging',
      gripInnerHtml: '<div class="column-grip"></div>',
      minWidth: minColumnWidth,
      headerOnly: true,
      hoverCursor: 'col-resize',
      dragCursor: 'col-resize',
      postbackSafe: true,
      flush: true,
      disable: !resizable,
    }
  );

  // Update resizer when resizable prop changes
  useEffect(() => {
    if (resizable) {
      enableResizer();
    } else {
      disableResizer();
    }
  }, [resizable, enableResizer, disableResizer]);

  const handleSelectRow = (id: string, selected: boolean) => {
    if (onSelectRow) {
      onSelectRow(id, selected);
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (onSelectAll) {
      onSelectAll(selected);
    }
  };

  return (
    <div className={`overflow-x-auto ${className}`}>
      <table 
        id={uniqueTableId}
        className={`min-w-full ${resizable ? 'resizable-table' : ''}`}
        style={{ tableLayout: 'fixed' }}
      >
        <colgroup>
          {onSelectRow && showCheckbox && (
            <col style={{ width: 50 }} />
          )}
          {headerMinWidths.map((w, i) => (
            <col key={`col-${i}`} style={{ width: w, minWidth: w }} />
          ))}
        </colgroup>
        <thead className="bg-[#F3F5F9] h-[40px]">
          <tr>
            {onSelectRow && showCheckbox && (
              <th
                scope="col"
                className={`px-3 py-3 text-left text-[14px] leading-[20px] font-medium text-[#5F6F84] bg-[#F3F5F9] uppercase tracking-wider whitespace-nowrap first:rounded-l-[12px]`}
                style={{ width: 50, minWidth: 50 }}
              >
                <TableCheckbox checked={isAllSelected} onChange={handleSelectAll} />
              </th>
            )}
            {columns.map((column, index) => (
              <TableHeader
                key={column.id}
                label={column.header}
                sortable={column.sortable}
                sortField={column.sortField}
                currentSortBy={currentSortBy}
                currentSortOrder={currentSortOrder}
                onSort={onSort}
                className={`${column.className} ${index === columns.length - 1 ? 'rounded-r-[12px]' : ''
                  }`}
                minWidth={headerMinWidths[index]}
              />
            ))}
          </tr>
        </thead>

        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((row, rowIndex) => (
            <tr
              key={row[idField]}
              className={`group h-[60px] ${rowClassName} hover:bg-blue-50`}
              ref={rowIndex === data.length - 1 ? lastItemRef : undefined}
            >

              {onSelectRow && showCheckbox && (
                <td className="px-3 py-2 whitespace-nowrap hover:text-[#2A2E34]">
                  <TableCheckbox
                    checked={selectedIds.includes(row[idField])}
                    onChange={selected => handleSelectRow(row[idField], selected)}
                  />
                </td>
              )}

              {columns.map(column => (
                <td
                  className="px-3 py-2 text-[#5F6F84] group-hover:text-[#2A2E34] overflow-hidden"
                  key={`${row[idField]}-${column.id}`}
                >
                  <div>
                    {column.cell(row, rowIndex)}
                  </div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {isLoading && (
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#3F73F6]"></div>
        </div>
      )}
    </div>
  );
};

export default DataTable;
