import React from 'react';
import { Event } from '@/types/event';
import { DataTable, Column } from '@/components/common';

interface EventsTableProps {
  events: Event[];
  title?: string;
  emptyMessage?: string;
  className?: string;
  onEventClick?: (event: Event) => void;
}

const EventsTable: React.FC<EventsTableProps> = ({ 
  events, 
  title = "Events",
  emptyMessage = "No events found for this contact.",
  className = '',
  onEventClick
}) => {
  // Create a wrapper component for clickable cells
  const ClickableCell: React.FC<{ event: Event; children: React.ReactNode; className?: string }> = ({ 
    event, 
    children, 
    className: cellClassName = '' 
  }) => {
    if (onEventClick) {
      return (
        <div 
          className={`cursor-pointer hover:opacity-75 transition-opacity ${cellClassName}`}
          onClick={(e) => {
            e.stopPropagation();
            onEventClick(event);
          }}
        >
          {children}
        </div>
      );
    }
    return <div className={cellClassName}>{children}</div>;
  };

  // Status badge component for events
  const EventStatusBadge: React.FC<{ status: string }> = ({ status }) => {
    const getStatusStyle = (status: string) => {
      switch (status?.toLowerCase()) {
        case 'completed':
          return {
            backgroundColor: '#97C7FF', // Light blue background
            color: '#2A2E34', // Dark text
          };
        case 'active':
          return {
            backgroundColor: '#8CF1BD', // Light green background
            color: '#2A2E34', // Dark text
          };
        case 'pending':
          return {
            backgroundColor: '#FEF3C7', // Light yellow background
            color: '#92400E', // Dark yellow/orange text
          };
        case 'cancelled':
          return {
            backgroundColor: '#FEE2E2', // Light red background
            color: '#991B1B', // Dark red text
          };
        default:
          return {
            backgroundColor: '#F3F4F6', // Light gray background
            color: '#374151', // Dark gray text
          };
      }
    };

    const style = getStatusStyle(status);

    return (
      <span
        className="inline-flex items-center justify-center px-3 py-1 rounded-full text-[12px] font-normal"
        style={{
          backgroundColor: style.backgroundColor,
          color: style.color,
        }}
      >
        {status}
      </span>
    );
  };

  // Define columns using the DataTable Column interface
  const columns: Column[] = [
    {
      id: 'eventType',
      header: 'EVENT TYPE',
      sortable: true,
      cell: (event: Event) => (
        <ClickableCell event={event} className="text-[14px] text-[#5F6F84]">
          {event.eventType}
        </ClickableCell>
      ),
    },
    {
      id: 'courtNoticeType',
      header: 'COURT NOTICE TYPE',
      sortable: true,
      cell: (event: Event) => (
        <ClickableCell event={event} className="text-[14px] text-[#5F6F84]">
          {event.courtNoticeType || '-'}
        </ClickableCell>
      ),
    },
    {
      id: 'attorney',
      header: 'ATTORNEY',
      sortable: true,
      cell: (event: Event) => (
        <ClickableCell event={event} className="text-[14px] text-[#2A2E34] font-medium">
          {event.attorney}
        </ClickableCell>
      ),
    },
    {
      id: 'start',
      header: 'START',
      sortable: true,
      cell: (event: Event) => (
        <ClickableCell event={event} className="text-[14px] text-[#5F6F84]">
          {event.start}
        </ClickableCell>
      ),
    },
    {
      id: 'end',
      header: 'END',
      sortable: true,
      cell: (event: Event) => (
        <ClickableCell event={event} className="text-[14px] text-[#5F6F84]">
          {event.end}
        </ClickableCell>
      ),
    },
    {
      id: 'location',
      header: 'LOCATION',
      sortable: true,
      cell: (event: Event) => (
        <ClickableCell event={event} className="text-[14px] text-[#5F6F84]">
          {event.location}
        </ClickableCell>
      ),
    },
    {
      id: 'status',
      header: 'STATUS',
      sortable: false,
      cell: (event: Event) => (
        <ClickableCell event={event}>
          <EventStatusBadge status={event.status} />
        </ClickableCell>
      ),
    },
  ];

  if (!events || events.length === 0) {
    return (
      <div className={`bg-white rounded-[12px] p-6 ${className}`}>
        <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">{title}</h3>
        <p className="text-[14px] text-[#5F6F84]">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`bg-white ${className}`}>
      {title && (
        <div className="mb-4">
          <h3 className="text-[16px] font-semibold text-[#2A2E34]">{title}</h3>
        </div>
      )}
      
      <DataTable
        columns={columns}
        data={events}
        showCheckbox={false}
        className=""
        idField="id"
        rowClassName={onEventClick ? 'hover:bg-[#F8F9FC] transition-colors' : 'hover:bg-[#F8F9FC] transition-colors'}
      />
    </div>
  );
};

export default EventsTable; 