import React, { ReactNode } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/layout/Layout';
import DetailSidebar from './DetailSidebar';
import { StatusBadge } from '@/components/common';
import { Poppins } from 'next/font/google';

// Font setup
const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

interface DetailPageLayoutProps {
  // Data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  entity: any; // Can be Contact or Client
  loading: boolean;
  error: string | null;
  
  // Navigation
  activeTab: string;
  onTabChange: (tab: string) => void;
  backUrl: string;
  backLabel: string;
  
  // Content
  children: ReactNode;
  
  // Customization
  entityName: string; // "Contact" or "Client"
  statusField?: string; // Field name for status badge
  className?: string;
}

const DetailPageLayout: React.FC<DetailPageLayoutProps> = ({
  entity,
  loading,
  error,
  activeTab,
  onTabChange,
  backUrl,
  backLabel,
  children,
  entityName,
  statusField = 'type',
  className = ''
}) => {
  const router = useRouter();

  if (loading) {
    return (
      <Layout>
        <div className={`flex h-full items-center justify-center ${poppins.className}`}>
          <div className="text-[16px] text-[#5F6F84]">Loading {entityName.toLowerCase()} details...</div>
        </div>
      </Layout>
    );
  }

  if (error || !entity) {
    return (
      <Layout>
        <div className={`flex h-full items-center justify-center ${poppins.className}`}>
          <div className="text-center">
            <div className="text-[16px] text-red-600 mb-4">
              {error || `${entityName} not found`}
            </div>
            <button
              onClick={() => router.push(backUrl)}
              className="px-4 py-2 bg-[#3F73F6] text-white rounded-[8px] text-[14px] font-medium hover:bg-[#2563EB] transition-colors"
            >
              {backLabel}
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className={`flex h-full overflow-hidden ${poppins.className} ${className}`}>
        <DetailSidebar activeTab={activeTab} onTabChange={onTabChange} />
        <div className="flex-1 overflow-auto bg-white">
          <div className="p-8">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={() => router.push(backUrl)}
                  className="text-[14px] text-[#3F73F6] hover:underline flex items-center"
                >
                  ← {backLabel}
                </button>
              </div>
              
              <div className="flex items-center space-x-4">
                <h1 className="text-[20px] font-medium text-[#2A2E34]">
                  {entity.name}
                </h1>
                <span className="text-[16px] text-[#5F6F84]">#{entity.id}</span>
                {entity[statusField] && (
                  <StatusBadge 
                    status={entity[statusField]} 
                    variant="filled"
                  />
                )}
              </div>
            </div>

            {/* Content */}
            <div className="bg-white">
              {children}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default DetailPageLayout; 