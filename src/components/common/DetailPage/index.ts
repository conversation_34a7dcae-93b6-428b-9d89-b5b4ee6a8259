export { default as DetailPageLayout } from './DetailPageLayout';
export { default as DetailSidebar } from './DetailSidebar';
export { default as InformationCard } from './InformationCard';
export { default as MattersTable } from './MattersTable';
export { default as EventsTable } from './EventsTable';
export { default as WorkflowsTable } from './WorkflowsTable';
export { default as FilesTable } from './FilesTable';

// Export types for better TypeScript support
export type { default as DetailSidebarProps } from './DetailSidebar';
export type { default as InformationCardProps } from './InformationCard';
export type { default as MattersTableProps } from './MattersTable';
export type { default as EventsTableProps } from './EventsTable';
export type { default as WorkflowsTableProps } from './WorkflowsTable';
export type { default as FilesTableProps } from './FilesTable';
export type { default as DetailPageLayoutProps } from './DetailPageLayout'; 