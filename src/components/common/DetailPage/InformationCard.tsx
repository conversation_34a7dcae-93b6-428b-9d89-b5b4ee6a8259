import React from 'react';

interface FieldConfig {
  id: string;
  label: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getValue: (data: any) => string;
  isLink?: boolean;
  colSpan?: number;
}

interface InformationCardProps {
  title: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;
  fields: FieldConfig[];
  className?: string;
}

const InformationCard: React.FC<InformationCardProps> = ({
  title,
  data,
  fields,
  className = ''
}) => {
  return (
    <div className={`bg-white border border-[#E5E9EB] rounded-[8px] ${className}`}>
      <div className="p-6 border-b border-[#E5E9EB]">
        <h3 className="text-[16px] font-medium text-[#2A2E34]">{title}</h3>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-4 gap-8">
          {fields.map((field) => (
            <div key={field.id} className={field.colSpan ? `col-span-${field.colSpan}` : ''}>
              <h4 className="text-[12px] font-medium text-[#2A2E34] mb-2 uppercase tracking-wide">
                {field.label}
              </h4>
              <p 
                className={`text-[14px] leading-[20px] ${
                  field.isLink 
                    ? 'text-[#3F73F6] cursor-pointer hover:underline' 
                    : 'text-[#5F6F84]'
                }`}
              >
                {field.getValue(data) || '-'}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default InformationCard; 