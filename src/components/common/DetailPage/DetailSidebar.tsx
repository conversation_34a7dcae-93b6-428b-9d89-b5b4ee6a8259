import React from 'react';

interface NavItem {
  id: string;
  title: string;
  icon?: string;
}

interface DetailSidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  navItems?: NavItem[];
  className?: string;
}

const defaultNavItems: NavItem[] = [
  { id: 'about', title: 'About' },
  { id: 'matters', title: 'Matters' },
  { id: 'events', title: 'Events' },
  { id: 'notes', title: 'Notes' },
  { id: 'workflows', title: 'Workflows' },
  { id: 'files', title: 'Files' },
  { id: 'communication', title: 'Communication' },
  { id: 'invoices', title: 'Invoices' },
  { id: 'payments', title: 'Payments' },
  { id: 'time-entries', title: 'Time Entries' },
  { id: 'expenses', title: 'Expenses' },
];

const DetailSidebar: React.FC<DetailSidebarProps> = ({ 
  activeTab, 
  onTabChange,
  navItems = defaultNavItems,
  className = ''
}) => {
  return (
    <aside className={`w-[200px] px-4 py-6 border-r border-[#E5E9EB] h-full bg-[#F8F9FC] ${className}`}>
      <div className="flex flex-col space-y-1">
        {navItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onTabChange(item.id)}
            className={`text-left px-4 py-3 text-[14px] font-medium transition-colors duration-200 rounded-[8px] ${
              activeTab === item.id
                ? 'bg-[#3F73F6] text-white'
                : 'text-[#5F6F84] hover:bg-[#E5E9EB] hover:text-[#2A2E34]'
            }`}
            aria-label={`Navigate to ${item.title}`}
          >
            {item.title}
          </button>
        ))}
      </div>
    </aside>
  );
};

export default DetailSidebar; 