import React from 'react';
import { ContactWorkflow } from '@/types/workflow';
import { DataTable, Column, StatusBadge } from '@/components/common';

interface WorkflowsTableProps {
  workflows: ContactWorkflow[];
  title?: string;
  emptyMessage?: string;
  className?: string;
  onWorkflowClick?: (workflow: ContactWorkflow) => void;
}

const WorkflowsTable: React.FC<WorkflowsTableProps> = ({ 
  workflows, 
  title = "Workflows",
  emptyMessage = "No workflows available.",
  className = '',
  onWorkflowClick
}) => {
  // Create a wrapper component for clickable cells
  const ClickableCell: React.FC<{ 
    workflow: ContactWorkflow; 
    children: React.ReactNode; 
    className?: string 
  }> = ({ 
    workflow, 
    children, 
    className: cellClassName = '' 
  }) => {
    if (onWorkflowClick) {
      return (
        <div 
          className={`cursor-pointer hover:opacity-75 transition-opacity ${cellClassName}`}
          onClick={(e) => {
            e.stopPropagation();
            onWorkflowClick(workflow);
          }}
        >
          {children}
        </div>
      );
    }
    return <div className={cellClassName}>{children}</div>;
  };

  // Define columns using the DataTable Column interface
  const columns: Column[] = [
    {
      id: 'workflowRun',
      header: 'WORKFLOW RUN',
      sortable: true,
      cell: (workflow: ContactWorkflow) => (
        <ClickableCell workflow={workflow} className="text-[14px] text-[#2A2E34]">
          {workflow.workflowRun}
        </ClickableCell>
      ),
    },
    {
      id: 'template',
      header: 'TEMPLATE',
      sortable: true,
      cell: (workflow: ContactWorkflow) => (
        <ClickableCell workflow={workflow} className="text-[14px] text-[#5F6F84]">
          {workflow.template}
        </ClickableCell>
      ),
    },
    {
      id: 'status',
      header: 'STATUS',
      sortable: true,
      cell: (workflow: ContactWorkflow) => (
        <ClickableCell workflow={workflow}>
          <StatusBadge status={workflow.status} />
        </ClickableCell>
      ),
    },
    {
      id: 'openDate',
      header: 'OPEN DATE',
      sortable: true,
      cell: (workflow: ContactWorkflow) => (
        <ClickableCell workflow={workflow} className="text-[14px] text-[#5F6F84]">
          {workflow.openDate}
        </ClickableCell>
      ),
    },
    {
      id: 'closedDate',
      header: 'CLOSED DATE',
      sortable: true,
      cell: (workflow: ContactWorkflow) => (
        <ClickableCell workflow={workflow} className="text-[14px] text-[#5F6F84]">
          {workflow.closedDate || '-'}
        </ClickableCell>
      ),
    },
    {
      id: 'dueDate',
      header: 'DUE DATE',
      sortable: true,
      cell: (workflow: ContactWorkflow) => (
        <ClickableCell workflow={workflow} className="text-[14px] text-[#5F6F84]">
          {workflow.dueDate}
        </ClickableCell>
      ),
    },
  ];

  if (!workflows || workflows.length === 0) {
    return (
      <div className={`bg-white rounded-[12px] p-6 ${className}`}>
        <h3 className="text-[16px] font-semibold text-[#2A2E34] mb-4">{title}</h3>
        <p className="text-[14px] text-[#5F6F84]">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`bg-white ${className}`}>
      {title && (
        <div className="mb-4">
          <h3 className="text-[16px] font-semibold text-[#2A2E34]">{title}</h3>
        </div>
      )}
      
      <DataTable
        columns={columns}
        data={workflows}
        showCheckbox={false}
        className=""
        idField="id"
        rowClassName={onWorkflowClick ? 'hover:bg-[#F8F9FC] transition-colors' : 'hover:bg-[#F8F9FC] transition-colors'}
      />
    </div>
  );
};

export default WorkflowsTable; 