import React from 'react';
import Image from 'next/image';

interface AvatarProps {
  name?: string;
  imageUrl?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Avatar: React.FC<AvatarProps> = ({ name, imageUrl, size = 'md', className = '' }) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-6 h-6 text-xs';
      case 'lg':
        return 'w-12 h-12 text-lg';
      case 'md':
      default:
        return 'w-8 h-8 text-sm';
    }
  };

  const getInitials = () => {
    if (!name) return '';
    const nameParts = name.split(' ');
    if (nameParts.length === 1) return name.charAt(0).toUpperCase();
    return (nameParts[0].charAt(0) + nameParts[1].charAt(0)).toUpperCase();
  };

  const sizeClasses = getSizeClasses();

  if (imageUrl) {
    return (
      <div className={`${sizeClasses} rounded-full overflow-hidden relative ${className}`}>
        <Image src={imageUrl} alt={name || 'User avatar'} layout="fill" objectFit="cover" />
      </div>
    );
  }

  if (!name || name === 'Unknown') {
    return (
      <div
        className={`${sizeClasses} rounded-full bg-blue-100 flex items-center justify-center text-blue-600 ${className}`}
      >
        <img src="/assets/avatar.svg" alt="User" className="w-6 h-6" />
      </div>
    );
  }

  return (
    <div
      className={`${sizeClasses} rounded-full bg-blue-100 flex items-center justify-center text-blue-600 ${className}`}
    >
      {getInitials()}
    </div>
  );
};

export default Avatar;
