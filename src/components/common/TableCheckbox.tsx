import React from 'react';
import CustomCheckbox from './CustomCheckbox';

interface TableCheckboxProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;
  borderColor?: string;
}

const TableCheckbox: React.FC<TableCheckboxProps> = ({
  checked = false,
  onChange,
  className = '',
  borderColor = '#C7D1DF',
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) {
      onChange(e.target.checked);
    }
  };

  return (
    <div className="flex items-center justify-center rounded-[12px]">
      <CustomCheckbox
        id={`table-checkbox-${Math.random().toString(36).substring(7)}`}
        checked={checked}
        onChange={handleChange}
        className={className}
        borderColor={borderColor}
      />
    </div>
  );
};

export default TableCheckbox;
