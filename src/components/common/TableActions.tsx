import React, { useMemo, useRef, useState } from 'react';
import SearchInput from './SearchInput';
import TableActionButton from './TableActionButton';
import Image from 'next/image';
import { convertStatusArrayToApiFormat } from '@/types/courtNotice';
import { ChevronDown, Plus, X } from 'lucide-react';
import CustomCheckbox from './CustomCheckbox';
import DateTimeModal from './DateTimeModal';

type Operator =
  | 'contains'
  | 'equals'
  | 'not_contains'
  | 'not_equals'
  | 'unassigned'
  | 'date_range'
  | 'before'
  | 'after'
  | 'between';
interface FilterRow {
  id: string;
  field: string;
  operator: Operator | '';
  value: string;
  selectedValues?: string[]; // For multi-select fields like assignee
}

// Fallback options (used only if API to fetch dynamic field options fails)
const DEFAULT_FIELD_OPTIONS = [
  'Name',
  'Assignee',
  'Status',
  'Contact',
  'Matter',
  'Templates',
  'Due date',
  'Create date',
  'Attorney',
];

const API_URL = process.env.NEXT_PUBLIC_BASE_URL;

interface TableActionsProps {
  searchValue: string;
  onSearchChange: (value: string) => void;
  onFilter?: () => void;
  onColumns?: (selected?: string[]) => void;
  onSaveView?: () => void;
  showFilter?: boolean;
  showColumns?: boolean;
  showSaveView?: boolean;
  disableSaveView?: boolean;
  className?: string;
  onApplyFilters?: (filters: FilterRow[]) => void;
  onFilteredDataReceived?: (data: unknown) => void;
  visibleColumns?: string[]; // Array of visible column names
  savedFilters?: Array<{
    fieldName: string;
    filter: string;
    value: string[];
  }>; // Array of saved filter configurations
  onFiltersChange?: (filters: FilterRow[]) => void; // Callback when filters change
}

interface ColumnField {
  name: string;
  isShowing: boolean;
}
export const TABLE_FIELD_NAME: ColumnField[] = [
  { name: 'Name', isShowing: false },
  { name: 'Started', isShowing: false },
  { name: 'Due On', isShowing: false },
  { name: 'Task Completed', isShowing: false },
  { name: 'Status', isShowing: false },
  { name: 'Activity', isShowing: false },
  { name: 'Assignee', isShowing: false },
  { name: 'Matter', isShowing: false },
  { name: 'Attorney', isShowing: false },
  { name: 'View', isShowing: false },
];

const TableActions: React.FC<TableActionsProps> = ({
  searchValue,
  onSearchChange,
  onFilter,
  onColumns,
  onSaveView,
  showFilter = true,
  showColumns = true,
  showSaveView = true,
  disableSaveView = false,
  className = '',
  onApplyFilters,
  onFilteredDataReceived,
  visibleColumns,
  savedFilters,
  onFiltersChange,
}) => {
  const [isColumnsOpen, setIsColumnsOpen] = useState(false);
  const [columnsSearch, setColumnsSearch] = useState('');
  const [columnsState, setColumnsState] = useState<ColumnField[]>(() => {
    if (visibleColumns) {
      // Standard logic: visible columns should be checked (isShowing: true)
      return TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: visibleColumns.includes(f.name),
      }));
    }
    // Default state when no visibleColumns provided - show typical default columns
    return TABLE_FIELD_NAME.map(f => ({
      ...f,
      isShowing: [
        'Name',
        'Started',
        'Due On',
        'Task Completed',
        'Status',
        'Activity',
        'Assignee',
      ].includes(f.name),
    }));
  });

  const [appliedColumns, setAppliedColumns] = useState<ColumnField[]>(() => {
    if (visibleColumns) {
      // Standard logic: visible columns should be checked (isShowing: true)
      return TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: visibleColumns.includes(f.name),
      }));
    }
    // Default state when no visibleColumns provided - show typical default columns
    return TABLE_FIELD_NAME.map(f => ({
      ...f,
      isShowing: [
        'Name',
        'Started',
        'Due On',
        'Task Completed',
        'Status',
        'Activity',
        'Assignee',
      ].includes(f.name),
    }));
  });

  // Update states when visibleColumns prop changes
  React.useEffect(() => {
    if (visibleColumns) {
      const newColumnsState = TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: visibleColumns.includes(f.name),
      }));
      setColumnsState(newColumnsState);
      setAppliedColumns(newColumnsState);
    } else {
      // Reset to default state when visibleColumns is undefined
      const defaultColumnsState = TABLE_FIELD_NAME.map(f => ({
        ...f,
        isShowing: [
          'Name',
          'Started',
          'Due On',
          'Task Completed',
          'Status',
          'Activity',
          'Assignee',
        ].includes(f.name),
      }));
      setColumnsState(defaultColumnsState);
      setAppliedColumns(defaultColumnsState);
    }
  }, [visibleColumns]);

  // Update filter states when savedFilters prop changes
  React.useEffect(() => {
    if (savedFilters && savedFilters.length > 0) {
      // Convert saved filters to FilterRow format
      const convertedFilters = savedFilters.map((savedFilter, _index) => {
        const fieldMap: Record<string, string> = {
          template_name: 'Name',
          assigned_users: 'Assignee',
          status: 'Status',
          contact: 'Contact',
          matter: 'Matter',
          templates: 'Templates',
          due_date: 'Due date',
          created_date: 'Create date',
          attorney: 'Attorney',
        };

        const operatorMap: Record<string, Operator> = {
          contains: 'contains',
          equal: 'equals',
          not_contains: 'not_contains',
          not_equal: 'not_equals',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[savedFilter.fieldName] || savedFilter.fieldName;
        const operator = (operatorMap[savedFilter.filter] || savedFilter.filter) as Operator;

        return {
          id: `saved-${_index}`,
          field: fieldName,
          operator: operator,
          value: savedFilter.value.join(', '),
          selectedValues: savedFilter.value,
        };
      });

      setFilters(convertedFilters);
      setAppliedFilters(convertedFilters);
    } else {
      // Reset to default state when savedFilters is undefined or empty
      const defaultFilters: FilterRow[] = [
        { id: 'default', field: 'Name', operator: '' as const, value: '', selectedValues: [] },
      ];
      setFilters(defaultFilters);
      setAppliedFilters(defaultFilters);
    }
  }, [savedFilters]);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const filterRef = useRef<HTMLDivElement>(null);
  const fieldDropdownRef = useRef<HTMLDivElement>(null);
  const operatorDropdownRef = useRef<HTMLDivElement>(null);
  const statusDropdownRef = useRef<HTMLDivElement>(null);
  const valueDropdownRef = useRef<HTMLDivElement>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState<FilterRow[]>(() => {
    if (savedFilters && savedFilters.length > 0) {
      // Convert saved filters to FilterRow format
      return savedFilters.map((savedFilter, _index) => {
        // Map backend field names to frontend field names
        const fieldMap: Record<string, string> = {
          template_name: 'Name',
          assigned_users: 'Assignee',
          status: 'Status',
          contact: 'Contact',
          matter: 'Matter',
          templates: 'Templates',
          due_date: 'Due date',
          created_date: 'Create date',
          attorney: 'Attorney',
        };

        // Map backend operators to frontend operators
        const operatorMap: Record<string, Operator> = {
          contains: 'contains',
          equal: 'equals',
          not_contains: 'not_contains',
          not_equal: 'not_equals',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[savedFilter.fieldName] || savedFilter.fieldName;
        const operator = (operatorMap[savedFilter.filter] || savedFilter.filter) as Operator;

        return {
          id: `saved-${_index}`,
          field: fieldName,
          operator: operator,
          value: savedFilter.value.join(', '), // Join array values for display
          selectedValues: savedFilter.value,
        };
      });
    }
    return [{ id: 'default', field: 'Name', operator: '' as const, value: '', selectedValues: [] }];
  });

  const [appliedFilters, setAppliedFilters] = useState<FilterRow[]>(() => {
    if (savedFilters && savedFilters.length > 0) {
      // Convert saved filters to FilterRow format - same logic as above
      return savedFilters.map((savedFilter, _index) => {
        const fieldMap: Record<string, string> = {
          template_name: 'Name',
          assigned_users: 'Assignee',
          status: 'Status',
          contact: 'Contact',
          matter: 'Matter',
          templates: 'Templates',
          due_date: 'Due date',
          created_date: 'Create date',
          attorney: 'Attorney',
        };

        const operatorMap: Record<string, Operator> = {
          contains: 'contains',
          equal: 'equals',
          not_contains: 'not_contains',
          not_equal: 'not_equals',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[savedFilter.fieldName] || savedFilter.fieldName;
        const operator = (operatorMap[savedFilter.filter] || savedFilter.filter) as Operator;

        return {
          id: `saved-${_index}`,
          field: fieldName,
          operator: operator,
          value: savedFilter.value.join(', '),
          selectedValues: savedFilter.value,
        };
      });
    }
    return [{ id: 'default', field: 'Name', operator: '' as const, value: '', selectedValues: [] }];
  });
  const [fieldOptions, setFieldOptions] = useState<string[]>(DEFAULT_FIELD_OPTIONS);
  const [fieldValueOptions, setFieldValueOptions] = useState<Record<string, string[]>>({});
  const [openValueDropdownId, setOpenValueDropdownId] = useState<string | null>(null);
  const [openFieldDropdownId, setOpenFieldDropdownId] = useState<string | null>(null);
  const [openOperatorDropdownId, setOpenOperatorDropdownId] = useState<string | null>(null);
  const [openStatusDropdownId, setOpenStatusDropdownId] = useState<string | null>(null);
  const [assigneeSearchValue, setAssigneeSearchValue] = useState<string>('');
  const [isDateModalOpen, setIsDateModalOpen] = useState(false);
  const [activeDateFilterId, setActiveDateFilterId] = useState<string | null>(null);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (dropdownRef.current && !dropdownRef.current.contains(target)) {
        setIsColumnsOpen(false);
      }
      if (filterRef.current && !filterRef.current.contains(target)) {
        setIsFilterOpen(false);
        // Close all filter dropdowns when filter panel closes
        setOpenFieldDropdownId(null);
        setOpenOperatorDropdownId(null);
        setOpenValueDropdownId(null);
        setOpenStatusDropdownId(null);
      }
    };
    if (isColumnsOpen || isFilterOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isColumnsOpen, isFilterOpen]);

  // Handle clicking outside of individual dropdowns
  React.useEffect(() => {
    const handleDropdownClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is outside of any open dropdown by checking if the target is not within the filter container
      if (filterRef.current && !filterRef.current.contains(target)) {
        // Close all dropdowns if click is outside the filter container
        setOpenFieldDropdownId(null);
        setOpenOperatorDropdownId(null);
        setOpenStatusDropdownId(null);
        setOpenValueDropdownId(null);
        setAssigneeSearchValue('');
      }
    };

    // Add event listener when any dropdown is open
    if (
      openFieldDropdownId ||
      openOperatorDropdownId ||
      openStatusDropdownId ||
      openValueDropdownId
    ) {
      document.addEventListener('mousedown', handleDropdownClickOutside);
    }

    return () => document.removeEventListener('mousedown', handleDropdownClickOutside);
  }, [openFieldDropdownId, openOperatorDropdownId, openStatusDropdownId, openValueDropdownId]);

  // Fetch dynamic filter field options from API (record keys)
  React.useEffect(() => {
    const controller = new AbortController();
    const fetchFieldOptions = async () => {
      try {
        // Limit to 1 record, we only need keys
        // Get userId from localStorage
        const user = localStorage.getItem('user');
        const userData = user ? JSON.parse(user) : null;
        const userId = userData?.id || 1; // fallback to 1 if not found

        const payload = {
          type: 'new_court_notice',
          page: 1,
          limit: 1,
          userId,
        };

        const res = await fetch(API_URL + '/workflow/court-notice-list', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', Accept: '*/*' },
          body: JSON.stringify(payload),
          signal: controller.signal,
        });

        if (!res.ok) throw new Error(`Failed to fetch: ${res.status}`);
        const json = await res.json();
        const first = json?.data?.fields?.[0] || {};
        const keys: string[] = Object.keys(first);
        if (keys.length) {
          // Sort for stable UI
          const sorted = keys.sort((a, b) => a.localeCompare(b));
          setFieldOptions(sorted);
          // If current selected field isn't part of new options, reset to first
          setFilters(prev => {
            const current = prev[0]?.field;
            if (!sorted.includes(current)) {
              const firstKey = sorted[0];
              return [
                {
                  id: `${Date.now()}`,
                  field: firstKey,
                  operator: '',
                  value: '',
                  selectedValues: [],
                },
              ];
            }
            return prev;
          });
        }
      } catch (e) {
        // eslint-disable-next-line no-console
        console.warn('Using default filter options due to API error', e);
      }
    };
    fetchFieldOptions();
    return () => controller.abort();
  }, []);

  const isDateField = (field: string) => {
    const f = (field || '').toLowerCase();
    return (
      f.includes('date') ||
      f === 'start_date' ||
      f === 'end_date' ||
      f === 'due_on' ||
      f === 'due date' ||
      f === 'create date' ||
      f === 'created_at'
    );
  };

  const ensureOperatorForField = (row: FilterRow, nextField: string): FilterRow => {
    if (isDateField(nextField)) {
      return { ...row, operator: '', value: '' };
    }
    return row;
  };

  const isStatusField = (field: string) => (field || '').toLowerCase() === 'status';

  const isAssigneeField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'assignee' || f === 'assign' || f === 'assigned_users';
  };

  const isMatterField = (field: string) => {
    const f = (field || '').toLowerCase();
    return f === 'matter' || f === 'matter_name' || f === 'matters';
  };

  const isTemplatesField = (field: string) => {
    const f = (field || '').toLowerCase();
    return (
      f === 'templates' || f === 'template' || f === 'template_name' || f === 'work_flow_runner'
    );
  };

  const isUserField = (field: string) => {
    const f = (field || '').toLowerCase();
    return (
      f === 'assign_by' ||
      f === 'assigned_users' ||
      f === 'assignee' ||
      f === 'user' ||
      f === 'users'
    );
  };

  const operatorOptionsForField = (
    field: string
  ): Array<{ value: Operator | ''; label: string; disabled?: boolean }> => {
    if (isDateField(field)) {
      return [
        { value: 'before', label: 'Before' },
        { value: 'after', label: 'After' },
        { value: 'between', label: 'Between' },
      ];
    }
    if (isStatusField(field)) {
      return [
        { value: 'equals', label: 'Equals' },
        { value: 'not_equals', label: 'Not equals' },
      ];
    }
    return [
      { value: '', label: 'Select' },
      { value: 'contains', label: 'Contains' },
      { value: 'equals', label: 'Equals' },
      { value: 'not_contains', label: 'Not contains' },
      { value: 'not_equals', label: 'Not equals' },
      { value: 'unassigned', label: 'Unassigned' },
      { value: 'date_range', label: 'Date range' },
    ];
  };

  const fetchDistinctValues = async (field: string): Promise<string[]> => {
    try {
      // Get userId from localStorage
      const user = localStorage.getItem('user');
      const userData = user ? JSON.parse(user) : null;
      const userId = userData?.id || 1; // fallback to 1 if not found

      const payload = {
        type: 'new_court_notice',
        page: 1,
        limit: 50,
        userId,
      };

      const res = await fetch(API_URL + '/workflow/court-notice-list', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Accept: '*/*' },
        body: JSON.stringify(payload),
      });

      if (!res.ok) throw new Error(`Failed to fetch: ${res.status}`);
      const json = await res.json();
      const rows: unknown[] = json?.data?.fields || [];
      const set = new Set<string>();
      for (const r of rows) {
        if (typeof r === 'object' && r !== null && field in r) {
          const v = (r as Record<string, unknown>)[field];
          if (v == null) continue;
          if (typeof v === 'string') {
            v.split(',')
              .map(s => s.trim())
              .filter(Boolean)
              .forEach(s => set.add(s));
          } else if (typeof v === 'boolean' || typeof v === 'number') {
            set.add(String(v));
          }
        }
      }
      return Array.from(set).sort((a, b) => a.localeCompare(b));
    } catch (err) {
      // eslint-disable-next-line no-console
      console.warn('distinct fetch failed', err);
      return [];
    }
  };

  const fetchMatters = async (): Promise<string[]> => {
    try {
      const token =
        localStorage.getItem('token') ||
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.iyZGHYkrmg7qUhm_MhoN9yGtGck8E9uyxhDEFEeitZY';
      const res = await fetch(API_URL + '/workflow/matter-list', {
        method: 'GET',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      if (!res.ok) throw new Error(`Failed to fetch matters: ${res.status}`);
      const json = await res.json();
      const matters: unknown[] = json?.data?.matters || [];

      // Extract matter names for the dropdown
      const matterNames = matters
        .map(matter => {
          if (typeof matter === 'object' && matter !== null && 'id' in matter) {
            const id = (matter as Record<string, unknown>).id;
            return typeof id === 'string' ? id : String(id);
          }
          return null;
        })
        .filter((id): id is string => typeof id === 'string');
      return Array.from(new Set(matterNames)).sort((a, b) => a.localeCompare(b));
    } catch (err) {
      // eslint-disable-next-line no-console
      console.warn('matter fetch failed', err);
      return [];
    }
  };

  const fetchUsers = async (): Promise<string[]> => {
    try {
      const token =
        localStorage.getItem('token') ||
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************.iyZGHYkrmg7qUhm_MhoN9yGtGck8E9uyxhDEFEeitZY';
      const res = await fetch(
        API_URL + '/workflow/user-list?search=&user_group_id=6877420fd4928f6a37ba1b96',
        {
          method: 'GET',
          headers: {
            Accept: 'application/json, text/plain, */*',
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      if (!res.ok) throw new Error(`Failed to fetch users: ${res.status}`);
      const json = await res.json();
      const users: unknown[] = json?.data?.users || json?.users || [];

      // Extract user names for the dropdown (assuming users have name or full_name field)
      const userNames = users
        .map(user => {
          if (typeof user === 'object' && user !== null && 'value' in user) {
            const value = (user as Record<string, unknown>).value;
            return typeof value === 'string' ? value : String(value);
          }
          return null;
        })
        .filter((value): value is string => typeof value === 'string');
      return Array.from(new Set(userNames)).sort((a, b) => a.localeCompare(b));
    } catch (err) {
      // eslint-disable-next-line no-console
      console.warn('user fetch failed', err);
      return [];
    }
  };

  const handleFilterChange = (id: string, key: keyof FilterRow, value: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f as FilterRow;
        if (key === 'field') {
          const next = ensureOperatorForField(
            {
              ...f,
              [key]: value,
              selectedValues:
                isAssigneeField(value) ||
                  isStatusField(value) ||
                  isMatterField(value) ||
                  isTemplatesField(value) ||
                  isUserField(value)
                  ? []
                  : f.selectedValues,
              value:
                isAssigneeField(value) ||
                  isStatusField(value) ||
                  isMatterField(value) ||
                  isTemplatesField(value) ||
                  isUserField(value)
                  ? ''
                  : f.value,
            } as FilterRow,
            value
          );
          // prefetch options when field changes
          (async () => {
            let opts: string[] = [];
            if (isMatterField(value)) {
              opts = await fetchMatters();
            } else if (isUserField(value)) {
              opts = await fetchUsers();
            } else {
              opts = await fetchDistinctValues(value);
            }
            setFieldValueOptions(prevMap => ({ ...prevMap, [value]: opts }));
          })();
          return next;
        }
        return { ...f, [key]: value } as FilterRow;
      })
    );
  };

  const handleAssigneeSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleStatusSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleMatterSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleTemplatesSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleUserSelection = (id: string, selectedValue: string) => {
    setFilters(prev =>
      prev.map(f => {
        if (f.id !== id) return f;
        const currentSelected = f.selectedValues || [];
        const isSelected = currentSelected.includes(selectedValue);
        const newSelected = isSelected
          ? currentSelected.filter(v => v !== selectedValue)
          : [...currentSelected, selectedValue];

        return {
          ...f,
          selectedValues: newSelected,
          value: newSelected.join(', '), // Keep value as comma-separated for display
        };
      })
    );
  };

  const handleDateModalOpen = (filterId: string) => {
    setActiveDateFilterId(filterId);
    setIsDateModalOpen(true);
  };

  const handleDateModalClose = () => {
    setIsDateModalOpen(false);
    setActiveDateFilterId(null);
  };

  const handleDateSelect = (date: string, _time: string) => {
    if (activeDateFilterId) {
      // The DateTimeModal now handles the range logic internally and sends the properly formatted value
      handleFilterChange(activeDateFilterId, 'value', date);
    }
  };

  const filteredFields = useMemo(
    () => columnsState.filter(opt => opt.name.toLowerCase().includes(columnsSearch.toLowerCase())),
    [columnsSearch, columnsState]
  );

  const isAllChecked = columnsState.every(field => field.isShowing);
  const selectedCount = useMemo(() => {
    if (visibleColumns && visibleColumns.length > 0) {
      // When visibleColumns is provided, show count of visible columns
      return visibleColumns.length;
    }
    // In normal mode, show count of selected columns
    const count = appliedColumns.filter(field => field.isShowing).length;
    // If no visibleColumns provided and no columns selected, show default count
    // This handles the case when pages don't provide visibleColumns initially
    if (count === 0 && !visibleColumns) {
      // Count the number of columns that would be visible by default
      // (Name, Started, Due On, Task Completed, Status, Activity, Assignee)
      // Matter and Attorney are not included in default visible columns
      return 7;
    }
    return count;
  }, [appliedColumns, visibleColumns]);
  const isSearching = columnsSearch.trim().length > 0;

  const toggleAll = (checked: boolean) => {
    setColumnsState(prev => prev.map(field => ({ ...field, isShowing: checked })));
  };

  const toggleSingle = (name: string, checked: boolean) => {
    setColumnsState(prev =>
      prev.map(field => (field.name === name ? { ...field, isShowing: checked } : field))
    );
  };

  const handleApply = () => {

    console.log(columnsState, "appliedColumns");
    setAppliedColumns(columnsState);
    setIsColumnsOpen(false);
    const selectedNames = columnsState.filter(f => f.isShowing).map(f => f.name);
    console.log(selectedNames, "selectedNames");
    onColumns?.(selectedNames);
  };

  const handleRemoveFilter = (id: string) => {
    setFilters(prev => prev.filter(f => f.id !== id));
  };

  const handleAddFilter = () => {
    setFilters(prev => [
      ...prev,
      { id: `${Date.now()}`, field: 'Name', operator: '', value: '', selectedValues: [] },
    ]);
  };

  // Calculate active filters count based on applied filters
  const getActiveFiltersCount = () => {
    return appliedFilters.filter(f => {
      // A filter is considered active if it has a valid field, operator, and value
      const hasValidField = f.field && f.field.trim() !== '';
      const hasValidOperator = f.operator !== '';

      // For multi-select fields, check selectedValues
      if (
        isAssigneeField(f.field) ||
        isStatusField(f.field) ||
        isMatterField(f.field) ||
        isTemplatesField(f.field) ||
        isUserField(f.field)
      ) {
        const hasValidValue = f.selectedValues && f.selectedValues.length > 0;
        return hasValidField && hasValidOperator && hasValidValue;
      }

      const hasValidValue = f.value && f.value.trim() !== '';

      // For unassigned operator, we don't need a value
      if (f.operator === 'unassigned') {
        return hasValidField && hasValidOperator;
      }

      return hasValidField && hasValidOperator && hasValidValue;
    }).length;
  };

  const applyFilters = () => {
    setIsFilterOpen(false);

    // Update applied filters to track changes
    setAppliedFilters(filters);

    // Call change detection callback if provided
    if (onFiltersChange) {
      onFiltersChange(filters);
    }

    // Call legacy callback for backward compatibility
    onFilter?.();
    // New typed callback with data if provided
    if (typeof onApplyFilters === 'function') {
      onApplyFilters(filters);
    }
    // Transform current filters to API payload and call API
    (async () => {
      // Field mapping from UI/API keys to backend expected keys
      const fieldMap: Record<string, string> = {
        // UI labels
        Name: 'template_name',
        Assignee: 'assigned_users',
        Status: 'status',
        Contact: 'contact',
        Matter: 'matter',
        Templates: 'work_flow_runner',
        'Due date': 'end_date',
        'Create date': 'start_date',
        Attorney: 'attorney',
        template_name: 'template_name',
        assigned_users: 'assigned_users',
        status: 'status',
        client_name: 'contact',
        matter: 'matter',
        matter_name: 'matter',
        work_flow_runner: 'work_flow_runner',
        end_date: 'end_date',
        due_on: 'end_date',
        start_date: 'start_date',
        created_at: 'start_date',
        attorney: 'attorney',
      };

      const opMap: Record<string, string> = {
        contains: 'contains',
        equals: 'equal',
        not_equals: 'not_equals',
        not_contains: 'does_not_contain',
        unassigned: 'equal',
      };

      // Only process valid filters (same logic as getActiveFiltersCount)
      const validFilters = filters.filter(f => {
        const hasValidField = f.field && f.field.trim() !== '';
        const hasValidOperator = f.operator || isDateField(f.field);
        const hasValidValue = f.value && f.value.trim() !== '';

        // For unassigned operator, we don't need a value
        if (f.operator === 'unassigned') {
          return hasValidField && hasValidOperator;
        }

        return hasValidField && hasValidOperator && hasValidValue;
      });

      const apiFilters = validFilters
        .map(row => {
          const fieldName = fieldMap[row.field] || row.field;
          if (!fieldName) return null;

          if (isDateField(row.field)) {
            if (row.operator === 'between') {
              const [start, end] = (row.value || '')
                .split('|')
                .map(s => s?.trim())
                .filter(Boolean);
              if (start && end) {
                return {
                  fieldName,
                  filter: 'date_range',
                  dateRange: {
                    from: start,
                    to: end,
                  },
                };
              }
              return null;
            } else if (row.operator === 'before') {
              const dateValue = (row.value || '').trim();
              if (dateValue) {
                return { fieldName, filter: 'date_before', value: [dateValue] };
              }
              return null;
            } else if (row.operator === 'after') {
              const dateValue = (row.value || '').trim();
              if (dateValue) {
                return { fieldName, filter: 'date_after', value: [dateValue] };
              }
              return null;
            }
            return null;
          }

          // Handle multi-select fields with multiple values
          if (
            (isAssigneeField(row.field) ||
              isStatusField(row.field) ||
              isMatterField(row.field) ||
              isTemplatesField(row.field) ||
              isUserField(row.field)) &&
            row.selectedValues &&
            row.selectedValues.length > 0
          ) {
            const apiOp = opMap[row.operator || ''] || 'contains';
            // Convert status values to API format (with underscores)
            const apiValues = isStatusField(row.field)
              ? convertStatusArrayToApiFormat(row.selectedValues)
              : row.selectedValues;
            return { fieldName, filter: apiOp, value: apiValues };
          }

          const apiOp = opMap[row.operator || ''] || 'contains';
          const v = (row.value || '').trim();

          // For unassigned, send empty string value
          if (row.operator === 'unassigned') {
            return { fieldName, filter: apiOp, value: [''] };
          }

          // For other operators, ensure we have a value
          if (!v) return null;

          // Convert status values to API format if it's a status field
          const apiValue = isStatusField(row.field) ? convertStatusArrayToApiFormat([v]) : [v];
          return { fieldName, filter: apiOp, value: apiValue };
        })
        .filter(Boolean);

      // Get userId from localStorage
      const user = localStorage.getItem('user');
      const userData = user ? JSON.parse(user) : null;
      const userId = userData?.id || 1; // fallback to 1 if not found

      const payload = {
        type: 'new_court_notice',
        page: 1,
        limit: 10,
        userId,
        filter: apiFilters,
      };

      // Log the payload for debugging
      // console.log('Sending filter payload:', JSON.stringify(payload, null, 2));
      // console.log('Active filters count:', getActiveFiltersCount());
      // console.log('Valid filters being sent:', apiFilters.length);

      try {
        const res = await fetch(API_URL + '/workflow/court-notice-list', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', Accept: '*/*' },
          body: JSON.stringify(payload),
        });

        if (!res.ok) {
          throw new Error(`API request failed with status: ${res.status}`);
        }

        const data = await res.json();
        // console.log('Filter API response:', data);

        // Call callback to update the parent component with filtered data
        if (onFilteredDataReceived) {
          onFilteredDataReceived(data);
        }
      } catch (err) {
        console.error('Filter API error:', err);
        // You might want to show a user-friendly error message here
        // setFilterError('Failed to apply filters. Please try again.');
      }
    })();
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <SearchInput value={searchValue} onChange={onSearchChange} placeholder="Search" />

      {showFilter && (
        <div className="relative" ref={filterRef}>
          <TableActionButton
            label={
              <div className="flex items-center gap-2">
                <span>Filter</span>
                {getActiveFiltersCount() > 0 && (
                  <span className="bg-[#3F73F6] text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
                    {getActiveFiltersCount()}
                  </span>
                )}
              </div>
            }
            icon={<Image src="/assets/filter.svg" alt="Filter" width={20} height={20} />}
            onClick={() => setIsFilterOpen(v => !v)}
            variant="secondary"
            width="w-[124px]"
            className={
              isFilterOpen
                ? 'ring-1 ring-[#3F73F6] border-transparent focus:outline-none focus:ring-blue-200 focus:border focus:border-[#3F73F6] focus:ring-2'
                : ''
            }
          />

          {isFilterOpen && (
            <div className="absolute right-0 mt-1 w-[724px] rounded-[16px] border border-[#E5E7EB] bg-white shadow-2xl z-50 p-[30px]">
              <div className="space-y-3">
                {filters.map(f => (
                  <div
                    key={f.id}
                    className="grid grid-cols-[32px_1.2fr_1fr_1.6fr] gap-3 items-center"
                  >
                    <button
                      type="button"
                      aria-label="Remove filter"
                      className="h-10 w-10 flex items-center justify-center rounded-[12px] border border-[#E5E7EB] text-[#5F6F84] hover:bg-[#F3F4F6]"
                      onClick={() => handleRemoveFilter(f.id)}
                    >
                      <X size={16} />
                    </button>

                    <div className="relative" ref={fieldDropdownRef}>
                      <button
                        className="h-10 w-full flex items-center justify-between rounded-[12px] border border-[#E5E7EB] px-3 text-sm text-[#2A2E34] bg-white focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer"
                        onClick={() =>
                          setOpenFieldDropdownId(openFieldDropdownId === f.id ? null : f.id)
                        }
                      >
                        {f.field || 'Select field'}
                        <ChevronDown
                          size={16}
                          className={`ml-2 text-[#6B7280] transition-transform ${openFieldDropdownId === f.id ? 'rotate-180' : ''
                            }`}
                        />
                      </button>

                      {/* Field dropdown list */}
                      {openFieldDropdownId === f.id && (
                        <ul className="absolute mt-2 w-full h-[270px] overflow-y-auto rounded-[12px] border border-[#E5E7EB] bg-white shadow-md overflow-hidden z-50">
                          {fieldOptions.map(opt => (
                            <li
                              key={opt}
                              onClick={() => {
                                handleFilterChange(f.id, 'field', opt);
                                setOpenFieldDropdownId(null);
                              }}
                              className="px-4 py-3 text-sm text-[#2A2E34] hover:bg-[#F9FAFB] cursor-pointer border-[#F3F4F6] last:border-0"
                            >
                              {opt}
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>

                    <div>
                      <div className="relative" ref={operatorDropdownRef}>
                        <button
                          className={`h-10 w-full flex items-center justify-between rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer ${f.operator === '' ? 'text-[#9AA4B2]' : 'text-[#2A2E34]'
                            }`}
                          onClick={() =>
                            setOpenOperatorDropdownId(openOperatorDropdownId === f.id ? null : f.id)
                          }
                        >
                          {f.operator || 'Select'}
                          <ChevronDown
                            size={16}
                            className={`ml-2 text-[#6B7280] transition-transform ${openOperatorDropdownId === f.id ? 'rotate-180' : ''
                              }`}
                          />
                        </button>

                        {/* Operator dropdown list */}
                        {openOperatorDropdownId === f.id && (
                          <ul className="absolute mt-2 w-full h-max-[270px] overflow-y-auto rounded-[12px] border border-[#E5E7EB] bg-white shadow-md overflow-hidden z-50">
                            {operatorOptionsForField(f.field).map(opt => (
                              <li
                                key={opt.value || 'empty'}
                                onClick={() => {
                                  handleFilterChange(f.id, 'operator', opt.value);
                                  setOpenOperatorDropdownId(null);
                                }}
                                className="px-4 py-3 text-sm text-[#2A2E34] hover:bg-[#F9FAFB] cursor-pointer border-[#F3F4F6] last:border-0"
                              >
                                {opt.label}
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    </div>

                    <div className="relative">
                      {isDateField(f.field) ? (
                        <div>
                          {f.operator === 'between' ? (
                            <button
                              type="button"
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm text-[#2A2E34] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                              onClick={() => handleDateModalOpen(f.id)}
                            >
                              <span
                                className={
                                  f.value &&
                                    f.value.includes('|') &&
                                    f.value.split('|')[0] &&
                                    f.value.split('|')[1]
                                    ? 'text-[#2A2E34]'
                                    : 'text-[#9AA4B2]'
                                }
                              >
                                {f.value &&
                                  f.value.includes('|') &&
                                  f.value.split('|')[0] &&
                                  f.value.split('|')[1]
                                  ? `${f.value.split('|')[0]} - ${f.value.split('|')[1]}`
                                  : 'Select date range'}
                              </span>
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M8 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M16 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M3.5 9H20.5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </button>
                          ) : f.operator === 'before' || f.operator === 'after' ? (
                            <button
                              type="button"
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm text-[#2A2E34] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                              onClick={() => handleDateModalOpen(f.id)}
                            >
                              <span className={f.value ? 'text-[#2A2E34]' : 'text-[#9AA4B2]'}>
                                {f.value || 'Select a date'}
                              </span>
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  d="M8 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M16 2V5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M3.5 9H20.5"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                                <path
                                  d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z"
                                  stroke="#5F6F84"
                                  strokeWidth="1.5"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                            </button>
                          ) : (
                            <div className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm text-[#9AA4B2] bg-[#F9FAFB] flex items-center">
                              Select date operator first
                            </div>
                          )}
                        </div>
                      ) : isStatusField(f.field) ? (
                        <div className="relative" ref={statusDropdownRef}>
                          <button
                            className="h-10 w-full flex items-center justify-between rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer"
                            onClick={async () => {
                              const isOpening = openStatusDropdownId !== f.id;
                              setOpenStatusDropdownId(isOpening ? f.id : null);
                              if (isOpening && !fieldValueOptions[f.field]) {
                                const opts = await fetchDistinctValues(f.field);
                                setFieldValueOptions(prevMap => ({ ...prevMap, [f.field]: opts }));
                              }
                              setOpenStatusDropdownId(openStatusDropdownId === f.id ? null : f.id);
                            }}
                          >
                            <div className="flex items-center gap-2">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <>
                                  <span
                                    className={`px-2 py-1 rounded-full text-xs font-medium ${f.selectedValues[0] === 'ON_TRACK' ||
                                      f.selectedValues[0] === 'On Track'
                                      ? 'bg-green-500 text-white'
                                      : f.selectedValues[0] === 'DUE_SOON' ||
                                        f.selectedValues[0] === 'Due Soon'
                                        ? 'bg-orange-500 text-white'
                                        : f.selectedValues[0] === 'OVERDUE' ||
                                          f.selectedValues[0] === 'Overdue'
                                          ? 'bg-red-500 text-white'
                                          : f.selectedValues[0] === 'COMPLETED' ||
                                            f.selectedValues[0] === 'Completed'
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-gray-500 text-white'
                                      }`}
                                  >
                                    {f.selectedValues[0]}
                                  </span>
                                  {f.selectedValues.length > 1 && (
                                    <span className="text-[#2A2E34] font-medium">
                                      +{f.selectedValues.length - 1}
                                    </span>
                                  )}
                                </>
                              ) : (
                                <span className="text-[#9AA4B2]">Select</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${openStatusDropdownId === f.id ? 'rotate-180' : ''
                                }`}
                            />
                          </button>

                          {/* Status dropdown list with checkboxes */}
                          {openStatusDropdownId === f.id && (
                            <div className="absolute left-0 right-0 top-11 z-50 bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                              <div className="p-4">
                                <div
                                  className="flex flex-col gap-4 overflow-x-auto pb-2 max-h-60"
                                  style={{
                                    minWidth: 'max-content',
                                    scrollbarWidth: 'thin',
                                    scrollbarColor: '#D1D5DB #F3F4F6',
                                  }}
                                >
                                  {(fieldValueOptions[f.field]?.length
                                    ? fieldValueOptions[f.field]
                                    : ['On Track', 'Due Soon', 'Overdue', 'Completed']
                                  ).map((opt, _index) => {
                                    const isSelected = f.selectedValues?.includes(opt) || false;
                                    return (
                                      <div
                                        key={opt}
                                        className="flex items-center gap-3 cursor-pointer flex-shrink-0 min-w-fit"
                                        onClick={() => {
                                          handleStatusSelection(f.id, opt);
                                        }}
                                      >
                                        <div className="relative">
                                          <div
                                            className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${isSelected
                                              ? 'bg-blue-600 border-blue-600'
                                              : 'bg-white border-gray-300 hover:border-gray-400'
                                              }`}
                                          >
                                            {isSelected && (
                                              <svg
                                                width="12"
                                                height="12"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                strokeWidth="3"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                className="text-white"
                                              >
                                                <polyline points="20,6 9,17 4,12" />
                                              </svg>
                                            )}
                                          </div>
                                        </div>
                                        <span
                                          className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap ${opt === 'On Track'
                                            ? 'bg-green-500 text-white'
                                            : opt === 'Due Soon'
                                              ? 'bg-orange-500 text-white'
                                              : opt === 'Overdue'
                                                ? 'bg-red-500 text-white'
                                                : opt === 'Completed'
                                                  ? 'bg-blue-500 text-white'
                                                  : 'bg-gray-500 text-white'
                                            }`}
                                        >
                                          {opt}
                                        </span>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : isAssigneeField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                            onClick={async () => {
                              const isOpening = openValueDropdownId !== f.id;
                              setOpenValueDropdownId(isOpening ? f.id : null);
                              if (isOpening && !fieldValueOptions[f.field]) {
                                const opts = await fetchDistinctValues(f.field);
                                setFieldValueOptions(prevMap => ({ ...prevMap, [f.field]: opts }));
                              }
                              if (!isOpening) {
                                setAssigneeSearchValue('');
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="text-[#3F73F6] font-medium">
                                  {f.selectedValues.length} selected
                                </span>
                              ) : (
                                <span className="text-[#9AA4B2]">Select assignees</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${openValueDropdownId === f.id ? 'rotate-180' : ''
                                }`}
                            />
                          </div>

                          {openValueDropdownId === f.id && (
                            <div className="absolute left-0 right-0 top-11 z-50 bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                              {/* Search input section */}
                              <div className="p-4 border-b border-[#E5E7EB]">
                                <div className="relative">
                                  <input
                                    className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                                    placeholder="Type a name"
                                    value={assigneeSearchValue}
                                    onChange={e => setAssigneeSearchValue(e.target.value)}
                                  />
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                    <svg
                                      width="20"
                                      height="20"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="text-[#9AA4B2]"
                                    >
                                      <circle cx="11" cy="11" r="8" />
                                      <path d="m21 21-4.35-4.35" />
                                    </svg>
                                  </div>
                                </div>
                              </div>

                              {/* Selected items section */}
                              {f.selectedValues && f.selectedValues.length > 0 && (
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  {/* <div className="text-[#3F73F6] text-sm font-medium mb-2">
                                    {f.selectedValues.length} selected
                                  </div> */}
                                  <div className="space-y-2">
                                    {f.selectedValues.map(selectedValue => (
                                      <div
                                        key={selectedValue}
                                        className="flex items-center justify-between text-sm"
                                      >
                                        <div className="flex items-center gap-3">
                                          <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                            <span className="text-white text-xs font-medium">
                                              {selectedValue
                                                .split(' ')
                                                .map(n => n[0])
                                                .join('')
                                                .substring(0, 2)
                                                .toUpperCase()}
                                            </span>
                                          </div>
                                          <span className="text-[#2A2E34] font-medium">
                                            {selectedValue}
                                          </span>
                                        </div>
                                        <button
                                          type="button"
                                          onClick={e => {
                                            e.stopPropagation();
                                            handleAssigneeSelection(f.id, selectedValue);
                                          }}
                                          className="text-[#9AA4B2] hover:text-[#EF4444] transition-colors"
                                        >
                                          <X size={16} />
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Suggestions section */}
                              <div className="p-4">
                                {/* <h3 className="text-sm font-medium text-[#2A2E34] mb-3">Suggestions</h3> */}
                                <div className="max-h-60 overflow-auto">
                                  {(fieldValueOptions[f.field] || [])
                                    .filter(opt =>
                                      opt.toLowerCase().includes(assigneeSearchValue.toLowerCase())
                                    )
                                    .slice(0, 50)
                                    .map((opt, _index) => {
                                      const isSelected = f.selectedValues?.includes(opt) || false;
                                      return (
                                        <div
                                          key={opt}
                                          className={`flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm transition-colors ${isSelected ? 'bg-[#EEF2FF] border border-[#C7D2FE]' : ''
                                            }`}
                                          onClick={() => {
                                            handleAssigneeSelection(f.id, opt);
                                          }}
                                        >
                                          <span
                                            className={`font-medium ${isSelected ? 'text-[#3F73F6]' : 'text-[#2A2E34]'}`}
                                          >
                                            {opt}
                                          </span>
                                          {isSelected && (
                                            <div className="ml-auto">
                                              <div className="w-4 h-4 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                                <svg
                                                  width="12"
                                                  height="12"
                                                  viewBox="0 0 24 24"
                                                  fill="none"
                                                  stroke="currentColor"
                                                  strokeWidth="2"
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  className="text-white"
                                                >
                                                  <polyline points="20,6 9,17 4,12" />
                                                </svg>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      );
                                    })}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : isMatterField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                            onClick={async () => {
                              const isOpening = openValueDropdownId !== f.id;
                              setOpenValueDropdownId(isOpening ? f.id : null);
                              if (isOpening && !fieldValueOptions[f.field]) {
                                const opts = await fetchMatters();
                                setFieldValueOptions(prevMap => ({ ...prevMap, [f.field]: opts }));
                              }
                              if (!isOpening) {
                                setAssigneeSearchValue('');
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="text-[#3F73F6] font-medium">
                                  {f.selectedValues.length} selected
                                </span>
                              ) : (
                                <span className="text-[#9AA4B2]">Select matters</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${openValueDropdownId === f.id ? 'rotate-180' : ''
                                }`}
                            />
                          </div>

                          {openValueDropdownId === f.id && (
                            <div className="absolute left-0 right-0 top-11 z-50 bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                              {/* Selected items section */}
                              {f.selectedValues && f.selectedValues.length > 0 && (
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  <div className="text-[#3F73F6] text-sm font-medium mb-2">
                                    {f.selectedValues.length} selected
                                  </div>
                                  <div className="space-y-2">
                                    {f.selectedValues.map(selectedValue => (
                                      <div
                                        key={selectedValue}
                                        className="flex items-center justify-between text-sm"
                                      >
                                        <div className="flex items-center gap-3">
                                          <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                            <span className="text-white text-xs font-medium">
                                              {selectedValue
                                                .split(' ')
                                                .map(n => n[0])
                                                .join('')
                                                .substring(0, 2)
                                                .toUpperCase()}
                                            </span>
                                          </div>
                                          <span className="text-[#2A2E34] font-medium">
                                            {selectedValue}
                                          </span>
                                        </div>
                                        <button
                                          type="button"
                                          onClick={e => {
                                            e.stopPropagation();
                                            handleMatterSelection(f.id, selectedValue);
                                          }}
                                          className="text-[#9AA4B2] hover:text-[#EF4444] transition-colors"
                                        >
                                          <X size={16} />
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Search input section */}
                              <div className="p-4 border-b border-[#E5E7EB]">
                                <div className="relative">
                                  <input
                                    className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                                    placeholder="Type a matter name"
                                    value={assigneeSearchValue}
                                    onChange={e => setAssigneeSearchValue(e.target.value)}
                                  />
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                    <svg
                                      width="20"
                                      height="20"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="text-[#9AA4B2]"
                                    >
                                      <circle cx="11" cy="11" r="8" />
                                      <path d="m21 21-4.35-4.35" />
                                    </svg>
                                  </div>
                                </div>
                              </div>

                              {/* Suggestions section */}
                              <div className="p-4">
                                {/* <h3 className="text-sm font-medium text-[#2A2E34] mb-3">Suggestions</h3> */}
                                <div className="max-h-60 overflow-auto">
                                  {(fieldValueOptions[f.field] || [])
                                    .filter(opt =>
                                      opt.toLowerCase().includes(assigneeSearchValue.toLowerCase())
                                    )
                                    .slice(0, 50)
                                    .map((opt, _index) => {
                                      const isSelected = f.selectedValues?.includes(opt) || false;
                                      return (
                                        <div
                                          key={opt}
                                          className={`flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm transition-colors ${isSelected ? 'bg-[#EEF2FF] border border-[#C7D2FE]' : ''
                                            }`}
                                          onClick={() => {
                                            handleMatterSelection(f.id, opt);
                                          }}
                                        >
                                          {/* <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                            <span className="text-white text-xs font-medium">
                                              {opt.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase()}
                                            </span>
                                          </div> */}
                                          <span
                                            className={`font-medium ${isSelected ? 'text-[#3F73F6]' : 'text-[#2A2E34]'}`}
                                          >
                                            {opt}
                                          </span>
                                          {isSelected && (
                                            <div className="ml-auto">
                                              <div className="w-4 h-4 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                                <svg
                                                  width="12"
                                                  height="12"
                                                  viewBox="0 0 24 24"
                                                  fill="none"
                                                  stroke="currentColor"
                                                  strokeWidth="2"
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  className="text-white"
                                                >
                                                  <polyline points="20,6 9,17 4,12" />
                                                </svg>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      );
                                    })}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : isTemplatesField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                            onClick={async () => {
                              const isOpening = openValueDropdownId !== f.id;
                              setOpenValueDropdownId(isOpening ? f.id : null);
                              if (isOpening && !fieldValueOptions[f.field]) {
                                const opts = await fetchDistinctValues(f.field);
                                setFieldValueOptions(prevMap => ({ ...prevMap, [f.field]: opts }));
                              }
                              if (!isOpening) {
                                setAssigneeSearchValue('');
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="text-[#3F73F6] font-medium">
                                  {f.selectedValues.length} selected
                                </span>
                              ) : (
                                <span className="text-[#9AA4B2]">Select templates</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${openValueDropdownId === f.id ? 'rotate-180' : ''
                                }`}
                            />
                          </div>

                          {openValueDropdownId === f.id && (
                            <div className="absolute left-0 right-0 top-11 z-50 bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                              {/* Selected items section */}
                              {f.selectedValues && f.selectedValues.length > 0 && (
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  <div className="text-[#3F73F6] text-sm font-medium mb-2">
                                    {f.selectedValues.length} selected
                                  </div>
                                  <div className="space-y-2">
                                    {f.selectedValues.map(selectedValue => (
                                      <div
                                        key={selectedValue}
                                        className="flex items-center justify-between text-sm"
                                      >
                                        <div className="flex items-center gap-3">
                                          <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                            <span className="text-white text-xs font-medium">
                                              {selectedValue
                                                .split(' ')
                                                .map(n => n[0])
                                                .join('')
                                                .substring(0, 2)
                                                .toUpperCase()}
                                            </span>
                                          </div>
                                          <span className="text-[#2A2E34] font-medium">
                                            {selectedValue}
                                          </span>
                                        </div>
                                        <button
                                          type="button"
                                          onClick={e => {
                                            e.stopPropagation();
                                            handleTemplatesSelection(f.id, selectedValue);
                                          }}
                                          className="text-[#9AA4B2] hover:text-[#EF4444] transition-colors"
                                        >
                                          <X size={16} />
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Search input section */}
                              <div className="p-4 border-b border-[#E5E7EB]">
                                <div className="relative">
                                  <input
                                    className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                                    placeholder="Type a template name"
                                    value={assigneeSearchValue}
                                    onChange={e => setAssigneeSearchValue(e.target.value)}
                                  />
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                    <svg
                                      width="20"
                                      height="20"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="text-[#9AA4B2]"
                                    >
                                      <circle cx="11" cy="11" r="8" />
                                      <path d="m21 21-4.35-4.35" />
                                    </svg>
                                  </div>
                                </div>
                              </div>

                              {/* Suggestions section */}
                              <div className="p-4">
                                {/* <h3 className="text-sm font-medium text-[#2A2E34] mb-3">Suggestions</h3> */}
                                <div className="max-h-60 overflow-auto">
                                  {(fieldValueOptions[f.field] || [])
                                    .filter(opt =>
                                      opt.toLowerCase().includes(assigneeSearchValue.toLowerCase())
                                    )
                                    .slice(0, 50)
                                    .map((opt, _index) => {
                                      const isSelected = f.selectedValues?.includes(opt) || false;
                                      return (
                                        <div
                                          key={opt}
                                          className={`flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm transition-colors ${isSelected ? 'bg-[#EEF2FF] border border-[#C7D2FE]' : ''
                                            }`}
                                          onClick={() => {
                                            handleTemplatesSelection(f.id, opt);
                                          }}
                                        >
                                          <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                            <span className="text-white text-xs font-medium">
                                              {opt
                                                .split(' ')
                                                .map(n => n[0])
                                                .join('')
                                                .substring(0, 2)
                                                .toUpperCase()}
                                            </span>
                                          </div>
                                          <span
                                            className={`font-medium ${isSelected ? 'text-[#3F73F6]' : 'text-[#2A2E34]'}`}
                                          >
                                            {opt}
                                          </span>
                                          {isSelected && (
                                            <div className="ml-auto">
                                              <div className="w-4 h-4 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                                <svg
                                                  width="12"
                                                  height="12"
                                                  viewBox="0 0 24 24"
                                                  fill="none"
                                                  stroke="currentColor"
                                                  strokeWidth="2"
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                  className="text-white"
                                                >
                                                  <polyline points="20,6 9,17 4,12" />
                                                </svg>
                                              </div>
                                            </div>
                                          )}
                                        </div>
                                      );
                                    })}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : isUserField(f.field) ? (
                        <div className="relative" ref={valueDropdownRef}>
                          <div
                            className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 text-sm bg-white focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6] hover:border-[#3F73F6] transition-colors cursor-pointer flex items-center justify-between"
                            onClick={async () => {
                              const isOpening = openValueDropdownId !== f.id;
                              setOpenValueDropdownId(isOpening ? f.id : null);
                              if (isOpening && !fieldValueOptions[f.field]) {
                                const opts = await fetchUsers();
                                setFieldValueOptions(prevMap => ({ ...prevMap, [f.field]: opts }));
                              }
                              if (!isOpening) {
                                setAssigneeSearchValue('');
                              }
                            }}
                          >
                            <div className="flex-1">
                              {f.selectedValues && f.selectedValues.length > 0 ? (
                                <span className="text-[#3F73F6] font-medium">
                                  {f.selectedValues.length} selected
                                </span>
                              ) : (
                                <span className="text-[#9AA4B2]">Select users</span>
                              )}
                            </div>
                            <ChevronDown
                              size={16}
                              className={`text-[#6B7280] transition-transform ${openValueDropdownId === f.id ? 'rotate-180' : ''
                                }`}
                            />
                          </div>

                          {openValueDropdownId === f.id && (
                            <div className="absolute left-0 right-0 top-11 z-50 bg-white border border-[#E5E7EB] rounded-[16px] max-h-96 overflow-hidden shadow-2xl">
                              {/* Selected items section */}
                              {f.selectedValues && f.selectedValues.length > 0 && (
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  <div className="text-[#3F73F6] text-sm font-medium mb-2">
                                    {f.selectedValues.length} selected
                                  </div>
                                  <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {f.selectedValues.map(selectedValue => (
                                      <div
                                        key={selectedValue}
                                        className="flex items-center justify-between text-sm"
                                      >
                                        <div className="flex items-center gap-3">
                                          <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                            <span className="text-white text-xs font-medium">
                                              {selectedValue
                                                .split(' ')
                                                .map(n => n[0])
                                                .join('')
                                                .substring(0, 2)
                                                .toUpperCase()}
                                            </span>
                                          </div>
                                          <span className="text-[#2A2E34] font-medium">
                                            {selectedValue}
                                          </span>
                                        </div>
                                        <button
                                          type="button"
                                          onClick={e => {
                                            e.stopPropagation();
                                            handleUserSelection(f.id, selectedValue);
                                          }}
                                          className="text-[#9AA4B2] hover:text-[#EF4444] transition-colors"
                                        >
                                          <X size={16} />
                                        </button>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Search input section */}
                              <div className="p-4 border-b border-[#E5E7EB]">
                                <div className="relative">
                                  <input
                                    className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                                    placeholder="Type a user name"
                                    value={assigneeSearchValue}
                                    onChange={e => setAssigneeSearchValue(e.target.value)}
                                  />
                                  <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                    <svg
                                      width="20"
                                      height="20"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      className="text-[#9AA4B2]"
                                    >
                                      <circle cx="11" cy="11" r="8" />
                                      <path d="m21 21-4.35-4.35" />
                                    </svg>
                                  </div>
                                </div>
                              </div>

                              {/* Suggestions section with horizontal scroll */}
                              <div className="p-4">
                                <div className="max-h-80 overflow-y-auto overflow-x-auto">
                                  <div className="grid grid-cols-1 gap-1 min-w-max">
                                    {(fieldValueOptions[f.field] || [])
                                      .filter(opt =>
                                        opt
                                          .toLowerCase()
                                          .includes(assigneeSearchValue.toLowerCase())
                                      )
                                      .slice(0, 50)
                                      .map((opt, _index) => {
                                        const isSelected = f.selectedValues?.includes(opt) || false;
                                        return (
                                          <div
                                            key={opt}
                                            className={`flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm transition-colors whitespace-nowrap ${isSelected
                                              ? 'bg-[#EEF2FF] border border-[#C7D2FE]'
                                              : ''
                                              }`}
                                            onClick={() => {
                                              handleUserSelection(f.id, opt);
                                            }}
                                          >
                                            <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                              <span className="text-white text-xs font-medium">
                                                {opt
                                                  .split(' ')
                                                  .map(n => n[0])
                                                  .join('')
                                                  .substring(0, 2)
                                                  .toUpperCase()}
                                              </span>
                                            </div>
                                            <span
                                              className={`font-medium ${isSelected ? 'text-[#3F73F6]' : 'text-[#2A2E34]'}`}
                                            >
                                              {opt}
                                            </span>
                                            {isSelected && (
                                              <div className="ml-auto">
                                                <div className="w-4 h-4 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                                  <svg
                                                    width="12"
                                                    height="12"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    strokeWidth="2"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    className="text-white"
                                                  >
                                                    <polyline points="20,6 9,17 4,12" />
                                                  </svg>
                                                </div>
                                              </div>
                                            )}
                                          </div>
                                        );
                                      })}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : isDateField(f.field) ? (
                        <div>
                          <div className="relative" ref={valueDropdownRef}>
                            <input
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                              placeholder="Type a name"
                              value={f.value}
                              onFocus={async () => {
                                // open dropdown and ensure options are fetched
                                setOpenValueDropdownId(f.id);
                                if (!fieldValueOptions[f.field]) {
                                  const opts = await fetchDistinctValues(f.field);
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: opts,
                                  }));
                                }
                              }}
                              onChange={e => {
                                handleFilterChange(f.id, 'value', e.target.value);
                                if (!openValueDropdownId) setOpenValueDropdownId(f.id);
                              }}
                              onBlur={() => {
                                // delay close to allow option onMouseDown
                                setTimeout(
                                  () =>
                                    setOpenValueDropdownId(prevId =>
                                      prevId === f.id ? null : prevId
                                    ),
                                  100
                                );
                              }}
                            />
                            <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-[#9AA4B2]"
                              >
                                <circle cx="11" cy="11" r="8" />
                                <path d="m21 21-4.35-4.35" />
                              </svg>
                            </div>
                          </div>
                          {openValueDropdownId === f.id &&
                            (fieldValueOptions[f.field]?.length || 0) > 0 && (
                              <div className="absolute left-0 right-0 top-11 z-50 bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                                {/* Search input inside dropdown */}
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  <div className="relative">
                                    <input
                                      className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                                      placeholder="Type a name"
                                      value={f.value}
                                      onChange={e => {
                                        handleFilterChange(f.id, 'value', e.target.value);
                                      }}
                                    />
                                    <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                      <svg
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="text-[#9AA4B2]"
                                      >
                                        <circle cx="11" cy="11" r="8" />
                                        <path d="m21 21-4.35-4.35" />
                                      </svg>
                                    </div>
                                  </div>
                                </div>

                                {/* Suggestions section */}
                                <div className="p-4">
                                  {/* <h3 className="text-sm font-medium text-[#2A2E34] mb-3">Suggestions</h3> */}
                                  <div className="max-h-60 overflow-auto">
                                    {(fieldValueOptions[f.field] || [])
                                      .filter(opt =>
                                        opt.toLowerCase().includes((f.value || '').toLowerCase())
                                      )
                                      .slice(0, 50)
                                      .map((opt, _index) => {
                                        // Generate avatar based on name
                                        // const _getInitials = (name: string) => {
                                        //     return name
                                        //       .split(' ')
                                        //       .map(n => n[0])
                                        //       .join('')
                                        //       .substring(0, 2)
                                        //       .toUpperCase();
                                        // };

                                        // const _getAvatarColor = (name: string) => {
                                        //   const colors = [
                                        //       'bg-blue-500',
                                        //       'bg-green-500',
                                        //       'bg-purple-500',
                                        //       'bg-pink-500',
                                        //       'bg-indigo-500',
                                        //       'bg-yellow-500',
                                        //       'bg-red-500',
                                        //       'bg-teal-500',
                                        //   ];
                                        //   const index = name.length % colors.length;
                                        //   return colors[index];
                                        // };

                                        // const _isPersonName =
                                        //   /^[A-Za-z\s]+$/.test(opt) && opt.includes(' ');

                                        return (
                                          <div
                                            key={opt}
                                            className="flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm text-[#2A2E34] transition-colors"
                                            onMouseDown={() => {
                                              // onMouseDown to avoid blur before click
                                              handleFilterChange(f.id, 'value', opt);
                                              setOpenValueDropdownId(null);
                                            }}
                                          >
                                            {/* {isPersonName ? (
                                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium ${getAvatarColor(opt)}`}>
                                              {getInitials(opt)}
                                            </div>
                                          ) : (
                                            <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                                                <circle cx="12" cy="7" r="4" />
                                              </svg>
                                            </div>
                                          )} */}
                                            <span className="font-medium">{opt}</span>
                                          </div>
                                        );
                                      })}
                                  </div>
                                </div>
                              </div>
                            )}
                        </div>
                      ) : (
                        <>
                          <div className="relative" ref={valueDropdownRef}>
                            <input
                              className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                              placeholder="Type a name"
                              value={f.value}
                              onFocus={async () => {
                                // open dropdown and ensure options are fetched
                                setOpenValueDropdownId(f.id);
                                if (!fieldValueOptions[f.field]) {
                                  const opts = await fetchDistinctValues(f.field);
                                  setFieldValueOptions(prevMap => ({
                                    ...prevMap,
                                    [f.field]: opts,
                                  }));
                                }
                              }}
                              onChange={e => {
                                handleFilterChange(f.id, 'value', e.target.value);
                                if (!openValueDropdownId) setOpenValueDropdownId(f.id);
                              }}
                              onBlur={() => {
                                // delay close to allow option onMouseDown
                                setTimeout(
                                  () =>
                                    setOpenValueDropdownId(prevId =>
                                      prevId === f.id ? null : prevId
                                    ),
                                  100
                                );
                              }}
                            />
                            <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                              <svg
                                width="20"
                                height="20"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-[#9AA4B2]"
                              >
                                <circle cx="11" cy="11" r="8" />
                                <path d="m21 21-4.35-4.35" />
                              </svg>
                            </div>
                          </div>
                          {openValueDropdownId === f.id &&
                            (fieldValueOptions[f.field]?.length || 0) > 0 && (
                              <div className="absolute left-0 right-0 top-11 z-50 bg-white border border-[#E5E7EB] rounded-[16px] max-h-80 overflow-hidden shadow-2xl">
                                {/* Search input inside dropdown */}
                                <div className="p-4 border-b border-[#E5E7EB]">
                                  <div className="relative">
                                    <input
                                      className="h-10 w-full rounded-[12px] border border-[#E5E7EB] px-3 pr-10 text-sm text-[#2A2E34] placeholder-[#9AA4B2] focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-[#3F73F6]"
                                      placeholder="Type a name"
                                      value={f.value}
                                      onChange={e => {
                                        handleFilterChange(f.id, 'value', e.target.value);
                                      }}
                                    />
                                    <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
                                      <svg
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        className="text-[#9AA4B2]"
                                      >
                                        <circle cx="11" cy="11" r="8" />
                                        <path d="m21 21-4.35-4.35" />
                                      </svg>
                                    </div>
                                  </div>
                                </div>

                                {/* Suggestions section */}
                                <div className="p-4">
                                  {/* <h3 className="text-sm font-medium text-[#2A2E34] mb-3">Suggestions</h3> */}
                                  <div className="max-h-60 overflow-auto">
                                    {(fieldValueOptions[f.field] || [])
                                      .filter(opt =>
                                        opt.toLowerCase().includes((f.value || '').toLowerCase())
                                      )
                                      .slice(0, 50)
                                      .map((opt, _index) => {
                                        // Generate avatar based on name
                                        // const _getInitials = (name: string) => {
                                        //   return name
                                        //     .split(' ')
                                        //     .map(n => n[0])
                                        //     .join('')
                                        //     .substring(0, 2)
                                        //     .toUpperCase();
                                        // };

                                        // const _getAvatarColor = (name: string) => {
                                        //   const colors = [
                                        //       'bg-blue-500',
                                        //       'bg-green-500',
                                        //       'bg-purple-500',
                                        //       'bg-pink-500',
                                        //       'bg-indigo-500',
                                        //       'bg-yellow-500',
                                        //       'bg-red-500',
                                        //       'bg-teal-500',
                                        //   ];
                                        //   const index = name.length % colors.length;
                                        //   return colors[index];
                                        // };

                                        // const _isPersonName =
                                        //   /^[A-Za-z\s]+$/.test(opt) && opt.includes(' ');

                                        return (
                                          <div
                                            key={opt}
                                            className="flex items-center gap-3 px-2 py-2.5 hover:bg-[#F3F4F6] cursor-pointer rounded-[8px] text-sm text-[#2A2E34] transition-colors"
                                            onMouseDown={() => {
                                              // onMouseDown to avoid blur before click
                                              handleFilterChange(f.id, 'value', opt);
                                              setOpenValueDropdownId(null);
                                            }}
                                          >
                                            {/* {isPersonName ? (
                                            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium ${getAvatarColor(opt)}`}>
                                              {getInitials(opt)}
                                            </div>
                                          ) : (
                                            <div className="w-8 h-8 rounded-full bg-[#3F73F6] flex items-center justify-center">
                                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                                                <circle cx="12" cy="7" r="4" />
                                              </svg>
                                            </div>
                                          )} */}
                                            <span className="font-medium">{opt}</span>
                                          </div>
                                        );
                                      })}
                                  </div>
                                </div>
                              </div>
                            )}
                        </>
                      )}
                    </div>
                  </div>
                ))}

                <div className="flex items-center justify-between pt-2">
                  <button
                    type="button"
                    className="flex items-center gap-2 text-[#3F73F6] px-3 py-2 rounded-[10px] border border-[#C7D2FE] hover:bg-[#EEF2FF]"
                    onClick={handleAddFilter}
                  >
                    <Plus size={18} />
                    <span className="text-sm font-medium">Add filter</span>
                  </button>

                  <button
                    type="button"
                    className="px-5 py-2 h-10 rounded-[10px] bg-[#3F73F6] text-white text-sm font-medium"
                    onClick={applyFilters}
                  >
                    Apply
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {showColumns && (
        <div className="relative" ref={dropdownRef}>
          <TableActionButton
            label="Columns"
            icon={isColumnsOpen && columnsSearch ? <ChevronDown size={20} /> : null}
            onClick={() => setIsColumnsOpen(v => !v)}
            variant="secondary"
            width="w-[124px]"
            className={
              isColumnsOpen
                ? 'ring-1 ring-[#3F73F6] border-transparent focus:outline-none focus:ring-blue-200 focus:border focus:border-[#3F73F6] focus:ring-2'
                : ''
            }
          >
            <span className="flex items-center gap-2">
              <span className={isColumnsOpen ? 'text-[#2A2E34]' : 'text-[#5F6F84]'}>Columns</span>
              {!isSearching && (
                <span className="min-w-[20px] h-[20px] px-[6px] rounded-full bg-[#3F73F6] text-white text-[12px] leading-[22px] flex items-center justify-center">
                  {selectedCount}
                </span>
              )}
            </span>
          </TableActionButton>

          {isColumnsOpen && (
            <div className="absolute right-0 mt-1 w-[277px] rounded-[12px] border border-[#E5E7EB] bg-white shadow-xl z-50">
              <div className="p-3">
                <div className="flex items-center gap-2 px-2 py-2 rounded-[4px] border border-[#E5E7EB]">
                  <Image src="/assets/search.svg" alt="Search" width={20} height={20} />
                  <input
                    value={columnsSearch}
                    onChange={e => setColumnsSearch(e.target.value)}
                    placeholder="Search columns"
                    className="bg-transparent outline-none text-sm text-[#5F6F84] w-full"
                  />
                </div>
              </div>

              <div className="max-h-[360px] overflow-y-auto bg-white border-[#DCE2EB] [scrollbar-width:thin] [scrollbar-color:#cbd5e0_#f7fafc] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-[#2A2E34] [&::-webkit-scrollbar-track]:bg-gray-100 text-base py-2">
                {!isSearching && (
                  <>
                    <div className="flex items-center gap-3 px-4 py-2 hover:bg-[#F9FAFB]">
                      <CustomCheckbox
                        id="columns-all"
                        checked={isAllChecked}
                        onChange={e => toggleAll(e.target.checked)}
                      />
                      <span className="text-sm text-[#2A2E34]">All</span>
                    </div>
                    <div className="h-px bg-[#E5E7EB] mx-4" />
                  </>
                )}

                {filteredFields.map(field => (
                  <div
                    key={field.name}
                    className="flex items-center gap-3 px-4 py-2 hover:bg-[#F9FAFB]"
                  >
                    <CustomCheckbox
                      id={`col-${field.name}`}
                      checked={field.isShowing}
                      onChange={e => toggleSingle(field.name, e.target.checked)}
                    />
                    <span className="text-sm text-[#2A2E34]">{field.name}</span>
                  </div>
                ))}
              </div>

              <div className="p-3 flex justify-end">
                <button
                  className="px-4 py-2 rounded-[4px] bg-[#3F73F6] text-white text-sm cursor-pointer"
                  onClick={handleApply}
                >
                  Apply
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {showSaveView && (
        <TableActionButton
          label="Save view"
          icon={<Image aria-hidden src="/assets/save.svg" alt="File icon" width={20} height={20} />}
          onClick={disableSaveView ? undefined : onSaveView}
          variant="outline"
          width="w-[134px]"
          disabled={disableSaveView}
        />
      )}

      {/* DateTimeModal for date selection */}
      <DateTimeModal
        isOpen={isDateModalOpen}
        onClose={handleDateModalClose}
        onSave={handleDateSelect}
        initialDate=""
        initialTime=""
        showTime={false}
        isRangePicker={
          activeDateFilterId
            ? filters.find(f => f.id === activeDateFilterId)?.operator === 'between'
            : false
        }
        _currentOperator={
          activeDateFilterId ? filters.find(f => f.id === activeDateFilterId)?.operator || '' : ''
        }
      />
    </div>
  );
};

export default TableActions;
