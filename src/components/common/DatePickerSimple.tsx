import React, { useState, useRef, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Calendar } from 'lucide-react';

interface DatePickerSimpleProps {
  selectedDate: Date | null;
  onDateChange: (date: Date | null) => void;
  onDateSelected?: (date: Date) => Promise<void>;
  disabled?: boolean;
  className?: string;
}

const DatePickerSimple: React.FC<DatePickerSimpleProps> = ({
  selectedDate,
  onDateChange,
  onDateSelected,
  disabled = false,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const datePickerRef = useRef<HTMLDivElement>(null);

  // Format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: '2-digit',
    });
  };

  // Handle date change
  const handleDateChange = async (date: Date | null) => {
    onDateChange(date);
    
    if (date && onDateSelected) {
      try {
        setIsLoading(true);
        setError(null);
        await onDateSelected(date);
      } catch (error) {
        setError('Failed to update date');
        console.error('Error updating date:', error);
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    } else {
      setIsOpen(false);
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={datePickerRef} className={`relative ${className}`}>
      <div 
        className="flex items-center justify-between border border-gray-300 rounded-xl overflow-hidden"
      >
        <input 
          type="text"
          value={selectedDate ? formatDate(selectedDate) : ''}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          readOnly
          placeholder="MM/DD/YY"
          className="px-4 py-3 text-left outline-none text-gray-600 w-28 text-sm cursor-pointer"
          disabled={disabled}
        />
        <div 
          onClick={() => !disabled && setIsOpen(!isOpen)}
          className="px-4 py-3 cursor-pointer bg-gray-50 border-l border-gray-300"
        >
          <Calendar size={18} className="text-gray-600" />
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-50 mt-1">
          <DatePicker
            selected={selectedDate}
            onChange={handleDateChange}
            inline
            onClickOutside={() => setIsOpen(false)}
            monthsShown={1}
            showMonthDropdown
            showYearDropdown
            dropdownMode="select"
          />
        </div>
      )}

      {isLoading && (
        <div className="absolute right-10 top-3">
          <div className="spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      
      {error && (
        <div className="absolute top-full left-0 right-0 mt-1 text-xs text-red-500 bg-red-50 p-1 rounded">
          {error}
        </div>
      )}
    </div>
  );
};

export default DatePickerSimple; 