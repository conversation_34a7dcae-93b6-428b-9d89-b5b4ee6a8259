import React, { useState, useCallback, useEffect } from 'react';
import Link, { LinkProps } from 'next/link';
import { FiChevronDown, FiChevronUp } from 'react-icons/fi';
import { MoreHorizontal, Edit2, Trash2 } from 'lucide-react';
import Image from 'next/image';
import { usePathname, useSearchParams } from 'next/navigation';
import { UserViewService, UserView } from '@/services/userViewService';
import RenameViewModal from '@/components/common/RenameViewModal';
import DeleteViewModal from '@/components/common/DeleteViewModal';
import { toast } from 'react-hot-toast';

// Types
interface MenuItemProps {
  href: LinkProps['href'];
  icon?: string;
  children: React.ReactNode;
  className?: string;
  indent?: boolean;
  variant?: 'default' | 'primary';
}

interface CollapsibleSectionProps {
  title: string;
  isCollapsed: boolean;
  onToggle: () => void;
  children: React.ReactNode;
  icon?: string;
  variant?: 'header' | 'folder';
}

interface ViewMenuProps {
  view: UserView;
  onRename: (view: UserView) => void;
  onDelete: (view: UserView) => void;
  isOpen: boolean;
  onToggle: () => void;
}

interface WorkflowSidebarProps {
  className?: string;
}

interface CollapsedSections {
  companyViews: boolean;
  legalOperations: boolean;
  allWorkflows: boolean;
  myViews: boolean;
}

// Constants
const STORAGE_KEY = 'workflow-sidebar-state';
const DEFAULT_COLLAPSED_STATE: CollapsedSections = {
  companyViews: true,
  legalOperations: true,
  allWorkflows: false,
  myViews: false,
};

// Utility functions for localStorage
const getStoredState = (): CollapsedSections => {
  if (typeof window === 'undefined') {
    return DEFAULT_COLLAPSED_STATE;
  }

  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      // Ensure all required keys exist
      return {
        companyViews: parsed.companyViews ?? DEFAULT_COLLAPSED_STATE.companyViews,
        legalOperations: parsed.legalOperations ?? DEFAULT_COLLAPSED_STATE.legalOperations,
        allWorkflows: parsed.allWorkflows ?? DEFAULT_COLLAPSED_STATE.allWorkflows,
        myViews: parsed.myViews ?? DEFAULT_COLLAPSED_STATE.myViews,
      };
    }
  } catch (error) {
    console.warn('Failed to parse stored sidebar state:', error);
  }

  return DEFAULT_COLLAPSED_STATE;
};

const setStoredState = (state: CollapsedSections): void => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to store sidebar state:', error);
  }
};

// Reusable MenuItem Component
const MenuItem: React.FC<MenuItemProps> = ({
  href,
  icon,
  children,
  className = '',
  indent = false,
  variant = 'default',
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isActive = React.useMemo(() => {
    if (typeof href === 'string') {
      return pathname === href;
    }

    const targetPath = href.pathname?.toString() ?? '';
    if (pathname !== targetPath) return false;

    const query = (href.query ?? {}) as Record<string, string | string[]>;
    const queryKeys = Object.keys(query);

    if (queryKeys.length === 0) return true;

    if (!searchParams) return false;

    for (const key of queryKeys) {
      const expected = query[key];
      const current = searchParams.get(key);
      if (Array.isArray(expected)) {
        if (!expected.includes(current ?? '')) return false;
      } else {
        if ((current ?? '') !== String(expected)) return false;
      }
    }
    return true;
  }, [pathname, href, searchParams]);

  const getItemClasses = () => {
    const baseClasses = 'flex items-center gap-2 text-sm';
    const indentClasses = indent ? 'pl-5' : '';
    const variantClasses = variant === 'primary' ? 'px-4 py-2' : 'p-2';

    return `${baseClasses} ${variantClasses} ${indentClasses} ${className}`;
  };

  const getSpanClasses = () => {
    const baseClasses = 'w-full font-medium';
    const paddingClasses = variant === 'primary' ? 'py-2 px-4' : 'py-2 px-3';
    const activeClasses = isActive
      ? 'bg-[#F3F5F9] text-[#3F73F6] rounded-[12px]'
      : 'text-[#5F6F84] hover:bg-gray-50 hover:rounded-[12px]';

    return `${baseClasses} ${paddingClasses} ${activeClasses}`;
  };

  // Prevent event propagation to avoid triggering parent handlers
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <Link href={href} className={getItemClasses()} onClick={handleClick}>
      {icon && <Image src={icon} alt="icon" width={16} height={16} />}
      <span className={getSpanClasses()}>{children}</span>
    </Link>
  );
};

// Reusable Collapsible Section Component
const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  isCollapsed,
  onToggle,
  children,
  icon,
  variant = 'header',
}) => {
  const getButtonClasses = () => {
    const baseClasses = 'w-full flex items-center justify-between text-sm';

    if (variant === 'header') {
      return `${baseClasses} px-4 py-2 font-medium text-[#5F6F84]`;
    }

    return `${baseClasses} gap-2 p-4 text-[#5F6F84] hover:bg-gray-50 ${
      isCollapsed ? 'text-[#2A2E34]' : ''
    }`;
  };

  const getTitleClasses = () => {
    if (variant === 'header') {
      return `font-medium text-[14px] ${isCollapsed ? '' : 'text-[#2A2E34]'}`;
    }

    return `${isCollapsed ? '' : 'text-[#2A2E34]'} font-medium`;
  };

  const renderIcon = () => {
    if (!icon) return null;

    const iconSrc = isCollapsed ? '/folder.svg' : '/folder-dark.svg';
    return <Image src={iconSrc} alt={title.toLowerCase()} width={16} height={16} />;
  };

  const renderChevron = () => {
    return isCollapsed ? <FiChevronDown size={16} /> : <FiChevronUp size={16} />;
  };

  // Ensure only the button click triggers the toggle
  const handleToggle = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onToggle();
    },
    [onToggle]
  );

  // Prevent clicks on the content area from propagating
  const handleContentClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <div onClick={handleContentClick}>
      <button className={getButtonClasses()} onClick={handleToggle} type="button">
        {variant === 'folder' ? (
          <div className="flex items-center gap-2">
            {renderIcon()}
            <span className={getTitleClasses()}>{title}</span>
          </div>
        ) : (
          <span className={getTitleClasses()}>{title}</span>
        )}
        {renderChevron()}
      </button>

      {!isCollapsed && <div className={variant === 'folder' ? 'ml-2' : 'mt-1'}>{children}</div>}
    </div>
  );
};

// ViewMenu Component
const ViewMenu: React.FC<ViewMenuProps> = ({ view, onRename, onDelete, isOpen, onToggle }) => {
  return (
    <div className="relative flex-shrink-0">
      <button
        onClick={e => {
          e.preventDefault();
          e.stopPropagation();
          onToggle();
        }}
        className="p-1 rounded-[4px] hover:bg-gray-200 transition-colors flex items-center justify-center"
      >
        <MoreHorizontal size={14} className="text-[#5F6F84]" />
      </button>

      {isOpen && (
        <>
          {/* Backdrop to close menu */}
          <div className="fixed inset-0 z-[50]" onClick={onToggle} />
          {/* Menu */}
          <div
            className="absolute right-0 top-full mt-2 bg-white border border-[#DCE2EB] rounded-[12px] shadow-lg z-[9999]"
            style={{
              width: '160px',
              height: '92px',
              paddingTop: '8px',
              paddingBottom: '8px',
              gap: '4px',
            }}
          >
            <button
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                onRename(view);
                onToggle();
              }}
              className="flex items-center gap-3 w-full px-4 py-2 text-sm text-[#5F6F84] hover:bg-[#F8F9FA] transition-colors"
            >
              <Edit2 size={16} className="text-[#5F6F84]" />
              Rename
            </button>
            <button
              onClick={e => {
                e.preventDefault();
                e.stopPropagation();
                onDelete(view);
                onToggle();
              }}
              className="flex items-center gap-3 w-full px-4 py-2 text-sm text-[#DC3545] hover:bg-[#FFF5F5] transition-colors"
            >
              <Trash2 size={16} className="text-[#DC3545]" />
              Delete
            </button>
          </div>
        </>
      )}
    </div>
  );
};

// Main Sidebar Component
const WorkflowSidebar: React.FC<WorkflowSidebarProps> = ({ className = '' }) => {
  const pathname = usePathname();

  // State management with localStorage persistence
  const [collapsedSections, setCollapsedSections] =
    useState<CollapsedSections>(DEFAULT_COLLAPSED_STATE);

  // State for user views
  const [userViews, setUserViews] = useState<UserView[]>([]);
  const [loadingViews, setLoadingViews] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // State for view menu and modals
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [renameModalOpen, setRenameModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedView, setSelectedView] = useState<UserView | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Initialize state from localStorage on client mount
  useEffect(() => {
    setIsClient(true);
    const storedState = getStoredState();
    setCollapsedSections(storedState);
  }, []);

  // Function to fetch user views
  const fetchUserViews = async () => {
    try {
      setLoadingViews(true);
      console.log('Fetching user views...');
      // Add cache busting parameter
      const response = await UserViewService.getUserViews();
      console.log('User views response:', response);
      if (response.data && response.data.success) {
        console.log('Setting user views:', response.data.views);
        setUserViews(response.data.views || []);
      } else {
        console.log('Response data structure:', response);
        setUserViews([]);
      }
    } catch (error) {
      console.error('Error fetching user views:', error);
      setUserViews([]);
    } finally {
      setLoadingViews(false);
    }
  };

  // Fetch user views on component mount
  useEffect(() => {
    fetchUserViews();
  }, []); // Run only once on mount

  // Expose refresh function globally
  useEffect(() => {
    (window as unknown as { refreshSidebarViews?: () => void }).refreshSidebarViews =
      fetchUserViews;
    return () => {
      delete (window as unknown as { refreshSidebarViews?: () => void }).refreshSidebarViews;
    };
  }, []);

  // Update localStorage whenever state changes
  useEffect(() => {
    if (isClient) {
      setStoredState(collapsedSections);
    }
  }, [collapsedSections, isClient]);

  // Handlers with proper isolation and persistence
  const handleCompanyViewsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      companyViews: !prev.companyViews,
    }));
  }, []);

  const handleLegalOperationsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      legalOperations: !prev.legalOperations,
    }));
  }, []);

  const handleAllWorkflowsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      allWorkflows: !prev.allWorkflows,
    }));
  }, []);

  const handleMyViewsToggle = useCallback(() => {
    setCollapsedSections(prev => ({
      ...prev,
      myViews: !prev.myViews,
    }));
  }, []);

  // Handlers for view menu actions
  const handleRename = useCallback((view: UserView) => {
    console.log('Rename clicked for view:', view.name);
    setSelectedView(view);
    setRenameModalOpen(true);
    setOpenMenuId(null);
  }, []);

  const handleDelete = useCallback((view: UserView) => {
    console.log('Delete clicked for view:', view.name);
    setSelectedView(view);
    setDeleteModalOpen(true);
    setOpenMenuId(null);
  }, []);

  const handleRenameSubmit = async (newName: string) => {
    if (!selectedView) return;

    try {
      setIsRenaming(true);
      const response = await UserViewService.renameUserView({
        viewId: selectedView.id,
        name: newName,
      });

      if (response.data.success) {
        setUserViews(views =>
          views.map(v => (v.id === selectedView.id ? { ...v, name: newName } : v))
        );
        toast.success('View renamed successfully');
        setRenameModalOpen(false);
        setSelectedView(null);
      } else {
        toast.error('Failed to rename view');
      }
    } catch (error) {
      console.error('Error renaming view:', error);
      toast.error('Failed to rename view');
    } finally {
      setIsRenaming(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedView) return;

    try {
      setIsDeleting(true);
      const response = await UserViewService.deleteUserView({
        viewId: selectedView.id,
      });

      if (response.data.success) {
        setUserViews(views => views.filter(v => v.id !== selectedView.id));
        toast.success('View deleted successfully');
        setDeleteModalOpen(false);
        setSelectedView(null);
      } else {
        toast.error('Failed to delete view');
      }
    } catch (error) {
      console.error('Error deleting view:', error);
      toast.error('Failed to delete view');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleModalClose = useCallback(() => {
    setRenameModalOpen(false);
    setDeleteModalOpen(false);
    setSelectedView(null);
  }, []);

  // Menu items data with stable references
  const primaryMenuItems = [
    {
      id: 'workflows',
      href: { pathname: '/all-work-flow', query: { type: 'my-workflow' } },
      icon: '/assets/workflow.svg',
      label: 'My Workflows',
    },
    {
      id: 'tasks',
      href: { pathname: '/all-work-flow', query: { type: 'my-tasks' } },
      icon: '/assets/task.svg',
      label: 'My Tasks',
    },
    {
      id: 'inbound',
      href: { pathname: '/all-work-flow', query: { type: 'inbound' } },
      icon: '/assets/inboud.svg',
      label: 'Inbound',
    },
  ];

  const legalOperationsItems = [
    {
      id: 'new-court-notice',
      href: { pathname: '/all-work-flow', query: { type: 'new-court-notice' } },
      label: 'New Court Notice',
    },
    {
      id: 'court-notice-followup',
      href: { pathname: '/all-work-flow', query: { type: 'court-notice-follow-up' } },
      label: 'Court Notice Follow up',
    },
  ];

  const allWorkflowsItems = [
    {
      id: 'completed',
      href: { pathname: '/all-work-flow', query: { type: 'completed' } },
      label: 'Completed',
    },
    {
      id: 'archived',
      href: { pathname: '/all-work-flow', query: { type: 'archived' } },
      label: 'Archived',
    },
  ];

  // Prevent any clicks on the sidebar container from propagating
  const handleSidebarClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <div
      className={`h-full px-4 py-5 bg-white border-r border-[#DCE2EB] w-[260px] overflow-y-auto ${className}`}
      style={{
        width: '260px',
        height: '1057px',
        borderRight: '1px solid #DCE2EB',
      }}
      onClick={handleSidebarClick}
    >
      <div className="flex flex-col">
        <div className="mb-2">
          <CollapsibleSection
            title="COMPANY VIEWS"
            isCollapsed={collapsedSections.companyViews}
            onToggle={handleCompanyViewsToggle}
            variant="header"
          >
            {/* Primary Menu Items */}
            {primaryMenuItems.map(item => (
              <MenuItem key={item.id} href={item.href} icon={item.icon} variant="primary">
                {item.label}
              </MenuItem>
            ))}

            {/* Legal Operations Submenu */}
            <CollapsibleSection
              title="Legal Operations"
              isCollapsed={collapsedSections.legalOperations}
              onToggle={handleLegalOperationsToggle}
              icon="/folder.svg"
              variant="folder"
            >
              {legalOperationsItems.map(item => (
                <MenuItem key={item.id} href={item.href} indent>
                  {item.label}
                </MenuItem>
              ))}
            </CollapsibleSection>

            {/* All Workflows Submenu */}
            <CollapsibleSection
              title="All Workflows"
              isCollapsed={collapsedSections.allWorkflows}
              onToggle={handleAllWorkflowsToggle}
              icon="/folder.svg"
              variant="folder"
            >
              {allWorkflowsItems.map(item => (
                <MenuItem key={item.id} href={item.href} indent>
                  {item.label}
                </MenuItem>
              ))}
            </CollapsibleSection>
          </CollapsibleSection>
        </div>

        {/* MY VIEWS Section */}
        <div className="mb-2">
          <CollapsibleSection
            title="MY VIEWS"
            isCollapsed={collapsedSections.myViews}
            onToggle={handleMyViewsToggle}
            variant="header"
          >
            {loadingViews ? (
              <div className="px-4 py-2 text-sm text-gray-500">Loading views...</div>
            ) : userViews.length > 0 ? (
              userViews.map(view => {
                const isActive = pathname === `/views/${view.id}`;

                return (
                  <div key={view.id} className="group relative">
                    <Link
                      href={`/views/${view.id}`}
                      className={`flex items-center px-4 py-3 text-sm rounded-[8px] mx-2 ${
                        isActive
                          ? 'bg-[#F3F5F9] text-[#3F73F6]'
                          : 'text-[#5F6F84] hover:bg-[#F8F9FA]'
                      }`}
                    >
                      <span className="truncate flex-1 font-medium">{view.name}</span>
                      <div
                        className="flex-shrink-0 ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={e => e.preventDefault()}
                      >
                        <ViewMenu
                          view={view}
                          onRename={handleRename}
                          onDelete={handleDelete}
                          isOpen={openMenuId === view.id}
                          onToggle={() => setOpenMenuId(openMenuId === view.id ? null : view.id)}
                        />
                      </div>
                    </Link>
                  </div>
                );
              })
            ) : (
              <div className="px-4 py-2 text-sm text-gray-500">No saved views</div>
            )}
          </CollapsibleSection>
        </div>
      </div>

      {/* Modals */}
      <RenameViewModal
        isOpen={renameModalOpen}
        onClose={handleModalClose}
        onSave={handleRenameSubmit}
        currentName={selectedView?.name || ''}
        isLoading={isRenaming}
      />

      <DeleteViewModal
        isOpen={deleteModalOpen}
        onClose={handleModalClose}
        onConfirm={handleDeleteConfirm}
        _viewName={selectedView?.name || ''}
        isLoading={isDeleting}
      />
    </div>
  );
};

export default WorkflowSidebar;
