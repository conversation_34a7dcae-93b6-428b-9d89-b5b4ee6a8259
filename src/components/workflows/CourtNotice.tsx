import React, { useState, useEffect, useRef, useCallback } from 'react';
import { X, AlertTriangle, Calendar, Plus, ChevronDown } from 'lucide-react';
import { workflowStyles, conditionalDisabled } from '@/styles/workflow';
import CourtNoticeEditModal from './modals/CourtNoticeEditModal';
// import CourtNoticeAddModal from './modals/CourtNoticeAddModal';
import Image from 'next/image';
import { apiGet, apiPut, apiDelete, apiPost } from '@/services/api/apiUtils';
import ReusableAlertModal from '@/components/ui/ReusableAlertModal';
import { countTbdFields } from '@/types/courtNotice';
import debounce from 'lodash/debounce';

// Define types for court notice data
export interface EventType {
  _id: string;
  id: string;
  caseNumber: string;
  clientName: string;
  description: string;
  matter_id?: string;
  selectedMatterId?: string;
  clientMatterId?: string;
  clientNamePart?: string;
  attendees?: string;
  notes?: string;
  attachments?: string[];
  files?: Array<{
    name: string;
    url?: string;
    type?: string;
    size?: number;
    key?: string;
  }>;
  deleteFiles?: Array<{
    name: string;
    key?: string;
  }>;

  // New fields for enhanced court notice functionality
  subject?: string;
  courtNoticeType?: string;
  court_notice_date?: string;
  appointmentAction?: string;
  eventStatus?: string;
  charge?: string;
  county?: string;
  courtLocation?: string;
  courtNoticeActions?: string;
  isAddSecondary?: boolean;

  // Updated date/time fields
  startDate?: string;
  endDate?: string;
  startTime: string;
  endTime: string;
  allDay?: boolean;
  allDayDeadLine?: boolean;

  // Attendees
  requiredAttendees?: string;
  optionalAttendees?: string;
  clientAttendance?: string;
  meetingLocation?: string;

  // Meeting details based on meeting location
  meetingLink?: string;
  phoneDetails?: string;
  meetingAddress?: string;

  // Legacy fields
  date?: string;
  isCompleted?: boolean;

  // Add new field to track TBD fields count
  tbdFieldsCount?: number;
  rescheduleAppointment?: string;
  appointmentToReschedule?: string;
  client_matter_id?: string;
  my_case_event_id?: string
}

export interface Matter {
  id: string;
  name: string;
  caseDescription?: string;
  matter_id?: string;
  _id: string;
  client_name: string;
  case_number: string;
  ex_county_of_arrest: string;
  client_id: string;
  is_active: boolean;
  text: string;
  my_case_matter_id?: string;
}

export interface ClientMatter {
  id: string;
  name: string;
  matters: Matter[];
  newClient?: boolean;
}

/**
 * Normalize a client/matter display string by removing duplicated matter names in parentheses.
 * Examples:
 * - "JOE LEAL | DWI BAC >= 0.15 (DWI BAC >= 0.15)" → "JOE LEAL | DWI BAC >= 0.15"
 * - "DWI BAC >= 0.15 (DWI BAC >= 0.15)" → "DWI BAC >= 0.15"
 * - Leaves other formats untouched.
 *
 * @param raw - Original label string (may contain client, matter and parenthetical)
 * @returns Cleaned label string without redundant parenthetical duplication
 */
const formatClientMatterName = (raw: string): string => {
  if (!raw) return raw;

  const trimLower = (s: string) => s.trim().replace(/\s+/g, ' ').toLowerCase();

  // Case 1: Contains a client prefix separated by a pipe
  if (raw.includes('|')) {
    const [left, rightPart] = raw.split('|');
    const right = (rightPart || '').trim();

    const match = right.match(/^(.*?)\s*\((.*?)\)\s*$/);
    if (match) {
      const base = match[1] || '';
      const paren = match[2] || '';
      if (trimLower(base) === trimLower(paren)) {
        return `${left.trim()} | ${base.trim()}`;
      }
    }
    return `${left.trim()} | ${right}`;
  }

  // Case 2: No client prefix, just "Matter (Matter)"
  const onlyMatch = raw.match(/^(.*?)\s*\((.*?)\)\s*$/);
  if (onlyMatch) {
    const base = onlyMatch[1] || '';
    const paren = onlyMatch[2] || '';
    if (trimLower(base) === trimLower(paren)) {
      return base.trim();
    }
  }

  return raw;
};

// Add new interface for API response type
export interface CourtNoticeApiData {
  clients: ClientMatter[];
  events: Record<string, EventType[]>;
}

export interface CourtNoticeProps {
  clients?: ClientMatter[];
  events?: Record<string, EventType[]>;
  onSave?: (matterId: string, events: EventType[], updatedEvent?: EventType) => void;
  onAddEvent?: (matterId: string) => void;
  onDeleteEvent?: (matterId: string, eventId: string) => void;
  onDeleteMatter?: (matterId: string) => void;
  onEditEvent?: (matterId: string, eventId: string, event: EventType) => void;
  onAddContact?: (status: boolean) => void;
  onDeleteClient?: (clientId: string) => void;
  onClientsUpdate?: (updatedClients: ClientMatter[]) => void;
  label?: string;
  fieldId?: string;
  workflowId?: string;
  isDisabled?: boolean;
  isUpdateMyCase?: boolean;
  isChildWorkflow?: boolean;
  user_group_id?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setClients?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setEvents?: any;
  isTaskReviewed?: boolean;
}

const CourtNotice: React.FC<CourtNoticeProps> = ({
  clients = [],
  events = {},
  setClients,
  setEvents,
  onSave,
  onAddEvent,
  onDeleteEvent,
  onDeleteMatter,
  onEditEvent,
  onAddContact,
  onDeleteClient,
  onClientsUpdate,
  // label = 'Review the court notice and ensure all the dates are correct.',
  fieldId,
  workflowId,
  isDisabled,
  options,
  isChildWorkflow,
  isUpdateMyCase,
  isTaskReviewed,
}) => {
  console.log('🚀 ~ workflowId:789', events);
  // State for managing clients and their events
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [currentEditEvent, setCurrentEditEvent] = useState<EventType | null>(null);
  const [currentMatterId, setCurrentMatterId] = useState<string>('');
  const [currentClientId, setCurrentClientId] = useState<string>('');
  const [selectedMatters, setSelectedMatters] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localOptions, setLocalOptions] = useState(options || null);
  const [confirmDeleteEventModalOpen, setConfirmDeleteEventModalOpen] = useState(false);
  const [confirmDeleteClientModalOpen, setConfirmDeleteClientModalOpen] = useState(false);
  const [confirmDeleteMatterModalOpen, setConfirmDeleteMatterModalOpen] = useState(false);
  const [pendingDeleteEventData, setPendingDeleteEventData] = useState<{
    matterId: string;
    eventId: string;
  } | null>(null);
  const [pendingDeleteClientId, setPendingDeleteClientId] = useState<string | null>(null);
  const [pendingDeleteMatterId, setPendingDeleteMatterId] = useState<string | null>(null);

  // States for searchable dropdown - updated to be client-specific
  const [clientSearchTerms, setClientSearchTerms] = useState<Record<string, string>>({});
  const [openDropdownClientId, setOpenDropdownClientId] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(true); // Default to edit mode
  const [searchResults, setSearchResults] = useState<Matter[]>([]);
  const [allMatters, setAllMatters] = useState<Matter[]>([]); // Store all matters
  const [isSearching, setIsSearching] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const defaultMatter: Matter = {
    _id: '',
    name: '',
    client_id: '',
    is_active: false,
    client_name: '',
    text: '',
    id: '',
    ex_county_of_arrest: '',
    case_number: '',
  };

  const [selectedMatter, setSelectedMatter] = useState<Matter>(defaultMatter);

  // State to track client_name for each selected matter
  const [selectedMatterClientNames, setSelectedMatterClientNames] = useState<
    Record<string, string>
  >({});

  // New state for keyboard navigation
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);

  const allDisableButtons = true;

  // Modified fetch function to handle both initial load and search
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const fetchMatters = useCallback(
    async (query: string = '') => {
      try {
        setIsSearching(true);

        // Build API URL
        const params: Record<string, string> = {};
        if (query.trim()) {
          params.search = query;
        }

        const response = await apiGet<{
          statusCode: number;
          data?: {
            matters: Matter[];
          };
        }>(`/workflow/matter-list`, {
          params,
        });

        if (response.data?.statusCode === 200 && response.data?.data?.matters) {
          const matters = response.data.data.matters;

          // Filter out already selected matters
          const filteredMatters = matters.filter(
            matter => !Object.values(selectedMatters).includes(matter.id)
          );

          // If this is the initial load (no search term), store all matters
          if (!query.trim()) {
            setAllMatters(filteredMatters);
            setSearchResults(filteredMatters);
            setIsInitialLoad(true);
          } else {
            // For search results, update searchResults but keep allMatters unchanged
            setSearchResults(filteredMatters);
          }
        } else {
          if (!query.trim()) {
            setAllMatters([]);
          }
          setSearchResults([]);
        }
      } catch (error) {
        console.error('Error fetching matters:', error);
        if (!query.trim()) {
          setAllMatters([]);
        }
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    },
    [selectedMatters]
  ); // Add selectedMatters to dependencies

  // Add this useEffect to load persisted selections on mount
  useEffect(() => {
    const savedSelections = localStorage.getItem('selectedMatters');
    if (savedSelections) {
      setSelectedMatters(JSON.parse(savedSelections));
    }
  }, []);

  // Debounced search function for search queries
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query || query.length < 2) {
        // Show all matters when search is cleared
        setSearchResults(allMatters);
        setIsSearching(false);
        return;
      }

      fetchMatters(query);
    }, 300),
    [fetchMatters, allMatters]
  );

  // Load all matters when dropdown opens for the first time
  useEffect(() => {
    if (openDropdownClientId && !isInitialLoad && allMatters.length === 0) {
      fetchMatters(''); // Fetch all matters without search term
    }
  }, [openDropdownClientId, isInitialLoad, allMatters.length, fetchMatters]);

  // Trigger search when search term changes for the currently open dropdown
  useEffect(() => {
    if (openDropdownClientId && isInitialLoad) {
      const searchTerm = clientSearchTerms[openDropdownClientId] || '';
      if (searchTerm && searchTerm.length >= 2) {
        debouncedSearch(searchTerm);
      } else {
        // Show all matters when no search term or search term is too short
        setSearchResults(allMatters);
      }
    }
  }, [clientSearchTerms, openDropdownClientId, debouncedSearch, allMatters, isInitialLoad]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpenDropdownClientId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Add a ref to track processed rescheduled events to prevent infinite loops
  const processedRescheduledEvents = useRef<Set<string>>(new Set());

  // Cleanup processed events on unmount
  useEffect(() => {
    return () => {
      processedRescheduledEvents.current.clear();
    };
  }, []);

  // Track if we've already fetched initial data
  const [initialDataFetched, setInitialDataFetched] = useState(false);

  // Track the last time a contact was added to force rerender
  const [lastContactAdded, setLastContactAdded] = useState<number>(0);

  // Track if the first contact has been added
  const [firstContactAdded, setFirstContactAdded] = useState(false);

  // Ref to track whether clients have been initialized from props
  const clientsInitialized = useRef(false);

  // Initialize client data and handle new clients
  useEffect(() => {
    if (clients?.length > 0) {
      // After clients are added (either initially or via add contact), set firstContactAdded
      if (!firstContactAdded && clients.length > 0) {
        setFirstContactAdded(true);
        console.log('Setting firstContactAdded=true from clients useEffect');
      }

      // Update selected matters for any new clients
      setSelectedMatters(prev => {
        const updatedSelectedMatters = { ...prev };

        // Set default selected matter for each client that doesn't have one yet
        clients.forEach(client => {
          // Only set if not already set
          if (updatedSelectedMatters[client.id] === undefined && client.matters.length > 0) {
            // Don't auto-select for clients added via "Add event to another contact"
            if (client.newClient) {
              updatedSelectedMatters[client.id] = '';
            } else {
              updatedSelectedMatters[client.id] = client.matters[0].id;
            }
          }
        });

        return updatedSelectedMatters;
      });
    }
  }, [clients, currentClientId, firstContactAdded]);

  // Initialize state from props only on mount (not on every prop change)
  useEffect(() => {
    // This effect should only run once to initialize from props
    if (clients?.length > 0 && !clientsInitialized.current && setClients) {
      // Only set clients once on mount if provided
      const initialClients = [...clients];
      clientsInitialized.current = true; // Mark as initialized to prevent future runs
      setClients(initialClients);
    }
  }, [setClients]); // Only depend on setClients, not clients

  // Add an effect to log and potentially handle new contact additions
  useEffect(() => {
    if (lastContactAdded > 0) {
      console.log(`Contact added at: ${new Date(lastContactAdded).toLocaleTimeString()}`);
      // You could do additional processing here if needed
    }
  }, [lastContactAdded]);

  // Add useEffect to handle fetching rescheduled events
  useEffect(() => {
    const fetchRescheduledEvents = async () => {
      try {
        const eventsToUpdate: Record<string, EventType[]> = {};
        const hasUpdates = false;

        // Check all events for rescheduled ones that need fetching
        for (const [matterId, matterEvents] of Object.entries(events)) {
          const updatedEvents = [...matterEvents];

          for (let i = 0; i < updatedEvents.length; i++) {
            // Check if this event needs rescheduled data and hasn't been processed yet
            // if (
            //   event?.appointmentAction === 'Cancel' &&
            //   event?.appointmentToReschedule &&
            //   !processedRescheduledEvents.current.has(event.appointmentToReschedule)
            // ) {
            //   try {
            //     const response = await apiGet<{
            //       statusCode: number;
            //       data?: EventType;
            //     }>(`/workflow/event/${event.appointmentToReschedule}`);
            //     if (response.data?.statusCode === 200 && response.data?.data) {
            //       const result = response.data?.data;
            //       // Preserve the original appointmentToReschedule value and set appointmentAction
            //       result.appointmentAction = 'Cancel';
            //       result.appointmentToReschedule = event.appointmentToReschedule;
            //       result.courtNoticeType = event.courtNoticeType;
            //       updatedEvents[i] = result;
            //       hasUpdates = true;
            //       // Mark this event as processed
            //       processedRescheduledEvents.current.add(event.appointmentToReschedule);
            //       console.log('✅ Retrieved rescheduled event:', response.data.data);
            //     }
            //   } catch (error) {
            //     console.error('❌ Error fetching rescheduled event:', error);
            //     // Mark as processed even if failed to prevent continuous retries
            //     processedRescheduledEvents.current.add(event.appointmentToReschedule);
            //   }
            // }
          }

          if (hasUpdates) {
            console.log('=============updatedEvents==', updatedEvents);
            eventsToUpdate[matterId] = updatedEvents;
          }
        }

        // Update events state if we have updates
        if (hasUpdates && Object.keys(eventsToUpdate).length > 0) {
          setEvents((prevEvents: Record<string, EventType[]>) => ({
            ...prevEvents,
            ...eventsToUpdate,
          }));
        }
      } catch (error) {
        console.error('Error in fetchRescheduledEvents:', error);
      }
    };

    // Only run if we have events and they haven't been processed for rescheduling yet
    if (Object.keys(events).length > 0 && setEvents) {
      fetchRescheduledEvents();
    }
  }, [events, setEvents]); // Run when events change

  // Fetch court notice data from API if fieldId and workflowId are provided and we haven't fetched data yet
  useEffect(() => {
    const fetchCourtNoticeData = async () => {
      // Skip fetch if we don't have required IDs or we've already fetched data
      if (!fieldId || !workflowId || initialDataFetched) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch court notice data
        const response = await apiGet<{
          statusCode: number;
          data?: {
            clients?: ClientMatter[];
            events?: Record<string, EventType[]>;
            options?: Record<string, string[]>;
          };
        }>(`/workflow/court-notice`, {
          params: {
            field_id: fieldId,
            workflow_id: workflowId,
          },
        });

        if (response.data.statusCode === 200 && response.data.data) {
          const apiData = response.data.data;

          // Set clients and events from API data
          if (apiData.clients) {
            setClients(apiData.clients);
            // If clients exist in initial data, consider first contact already added
            if (apiData.clients.length > 0) {
              setFirstContactAdded(true);
              console.log('Initial data includes clients, firstContactAdded set to true');
            }
          }

          if (apiData.events) {
            // Process rescheduled events before setting the events
            const processedEvents = { ...apiData.events };

            for (const [matterId, matterEvents] of Object.entries(processedEvents)) {
              const updatedEvents = [...matterEvents];
              let hasUpdates = false;

              for (let i = 0; i < updatedEvents.length; i++) {
                const event = updatedEvents[i];

                // Check if this event needs rescheduled data and hasn't been processed yet
                if (
                  event?.appointmentAction === 'Cancel' &&
                  event?.appointmentToReschedule &&
                  !processedRescheduledEvents.current.has(event.appointmentToReschedule)
                ) {
                  try {
                    const rescheduledResponse = await apiGet<{
                      statusCode: number;
                      data?: EventType;
                    }>(`/workflow/event/${event.appointmentToReschedule}`);

                    if (
                      rescheduledResponse.data?.statusCode === 200 &&
                      rescheduledResponse.data?.data
                    ) {
                      const result = rescheduledResponse.data?.data;
                      // Preserve the original appointmentToReschedule value and set appointmentAction
                      result.appointmentAction = 'Cancel';
                      result.appointmentToReschedule = event.appointmentToReschedule;
                      result.courtNoticeType = event.courtNoticeType;

                      updatedEvents[i] = result;
                      hasUpdates = true;

                      // Mark this event as processed
                      processedRescheduledEvents.current.add(event.appointmentToReschedule);

                      console.log(
                        '✅ Retrieved rescheduled event during initial load:',
                        rescheduledResponse.data.data
                      );
                    }
                  } catch (error) {
                    console.error(
                      '❌ Error fetching rescheduled event during initial load:',
                      error
                    );
                    // Mark as processed even if failed to prevent continuous retries
                    processedRescheduledEvents.current.add(event.appointmentToReschedule);
                  }
                }
              }

              if (hasUpdates) {
                processedEvents[matterId] = updatedEvents;
              }
            }

            setEvents(processedEvents);
          }

          // Store options for dropdown fields if provided
          // Combine with options from props if available
          if (apiData.options) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            setLocalOptions((prevOptions: any) => ({
              ...prevOptions,
              ...apiData.options,
              ...options, // Overlay with options from props (higher priority)
            }));
          }

          // Mark that we've fetched initial data
          setInitialDataFetched(true);
        }
      } catch (err) {
        console.error('Error fetching court notice data:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch court notice data');
      } finally {
        setLoading(false);
      }
    };

    fetchCourtNoticeData();
  }, [fieldId, workflowId, initialDataFetched, options]);

  // Handle matter selection change
  const handleMatterChange = useCallback(
    (clientId: string, matterId: string) => {
      const selectedMatter =
        allMatters.find(matter => matter.id === matterId) ||
        searchResults.find(matter => matter.id === matterId);

      if (selectedMatter) {
        setSelectedMatters(prev => {
          const newSelectedMatters = { ...prev, [clientId]: matterId };
          localStorage.setItem('selectedMatters', JSON.stringify(newSelectedMatters));
          return newSelectedMatters;
        });

        // Find the selected matter from search results
        // const selectedMatter = searchResults.find(matter => matter.id === matterId);

        if (selectedMatter) {
          // Store the client_name for this matter
          setSelectedMatterClientNames((prev: Record<string, string>) => ({
            ...prev,
            [matterId]: selectedMatter.client_name || '',
          }));

          setSelectedMatter(selectedMatter);

          // Store selectedMatter.my_matter_id in localStorage
          if (selectedMatter.my_case_matter_id) {
            localStorage.setItem('selectedMatterMyMatterId', selectedMatter.my_case_matter_id);
            console.log('Stored my_matter_id in localStorage:', selectedMatter.my_case_matter_id);
          }

          if (setClients) {
            // Add the selected matter to the client's matters array if it's not already there
            setClients((prevClients: ClientMatter[]) => {
              const updatedClients = prevClients.map(client => {
                if (client.id === clientId) {
                  // Check if this matter is already in the client's matters array
                  const matterExists = client.matters.some(
                    matter =>
                      matter.id === matterId || matter._id === matterId || matter.matter_id === matterId
                  );

                  if (!matterExists) {
                    // Add the matter to the client's matters array
                    const updatedClient = {
                      ...client,
                      matters: [...client.matters, selectedMatter],
                    };

                    console.log(`Added matter ${selectedMatter.name} to client ${client.name}`);
                    return updatedClient;
                  }
                }
                return client;
              });

              // Call onClientsUpdate to notify parent of the change
              if (onClientsUpdate) {
                onClientsUpdate(updatedClients);
              }

              return updatedClients;
            });

            // Initialize events for this matter if not already present
            if (setEvents) {
              setEvents((prevEvents: Record<string, EventType[]>) => {
                if (!prevEvents[matterId]) {
                  return {
                    ...prevEvents,
                    [matterId]: [],
                  };
                }
                return prevEvents;
              });
            }
          }
        }

        // Reset events UI for this client if needed
        // This ensures that when a different matter is selected,
        // the UI updates to show events for that matter

        // Update any other relevant state or UI elements
        // For example, you might want to clear any edit modal state
        if (currentClientId === clientId && currentMatterId !== matterId) {
          setCurrentMatterId(matterId);
          setCurrentEditEvent(null);
          setEditModalOpen(false);
        }
      }
    },
    [
      allMatters,
      searchResults,
      setClients,
      setEvents,
      onClientsUpdate,
      currentClientId,
      currentMatterId,
    ]
  );

  // Mark an event as completed/reviewed
  const toggleEventStatus = async (matterId: string, eventId: string) => {
    try {
      const matterEvents = [...(events[matterId] || [])];
      const eventIndex = matterEvents.findIndex(event => event.id === eventId);

      if (eventIndex !== -1) {
        const updatedEvent = {
          ...matterEvents[eventIndex],
          isCompleted: !matterEvents[eventIndex].isCompleted,
        };

        // Count TBD fields and add to the event
        updatedEvent.tbdFieldsCount = countTbdFields(updatedEvent);

        matterEvents[eventIndex] = updatedEvent;

        // Update local state first for immediate UI feedback
        setEvents((prev: Record<string, EventType[]>) => ({
          ...prev,
          [matterId]: matterEvents,
        }));

        // Call API to update event status if fieldId and workflowId are available
        if (fieldId && workflowId) {
          await apiPut(`/workflow/court-notice/event`, {
            field_id: fieldId,
            workflow_id: workflowId,
            matter_id: matterId,
            event_id: eventId,
            event: updatedEvent,
          });
        }

        // Call onSave if provided
        if (onSave) {
          onSave(matterId, matterEvents);
        }
      }
    } catch (error) {
      console.error('Error updating event status:', error);
      // Revert to previous state on error
      setEvents((prev: Record<string, EventType[]>) => ({ ...prev }));
      setError('Failed to update event status. Please try again.');
    }
  };

  // Remove an event
  const removeEvent = async (matterId: string, eventId: string) => {
    // Set pending delete data and show confirmation modal instead of using confirm()
    // console.log('🚀 ~ removeEvent ~ matterId:', matterId,eventId);
    setPendingDeleteEventData({ matterId, eventId });
    setConfirmDeleteEventModalOpen(true);
  };

  // Handle confirmed event deletion
  const handleConfirmEventDelete = async () => {
    if (!pendingDeleteEventData) return;

    const { matterId, eventId } = pendingDeleteEventData;

    try {
      // Update local state first for immediate UI feedback
      // console.log('🚀 ~ handleConfirmEventDelete ~ dhyey matterId:', matterId, eventId);
      const matterEvents = events[matterId]
        ? events[matterId].filter(event => event.id !== eventId)
        : [];

      // console.log('🚀 ~ handleConfirmEventDelete ~ dhyey fieldId:', matterEvents);

      setEvents((prev: Record<string, EventType[]>) => ({
        ...prev,
        [matterId]: matterEvents,
      }));

      // Call API to delete event if fieldId and workflowId are available
      if (fieldId && workflowId) {
        await apiDelete(`/workflow/court-notice/event`, {
          data: {
            field_id: fieldId,
            workflow_id: workflowId,
            matter_id: matterId,
            event_id: eventId,
          },
        });
      }

      // Call onDeleteEvent if provided
      if (onDeleteEvent) {
        onDeleteEvent(matterId, eventId);
      }

      // Call onSave if provided to persist changes
      if (onSave) {
        onSave(matterId, matterEvents);
      }

      // Update AdvisorPanel event count if the global function is available
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
        // Find the deleted event to get its client name
        const deletedEvent = events[matterId]?.find(event => event.id === eventId);
        if (deletedEvent) {
          // Use clientName first, fallback to subject if needed
          const eventClientName = deletedEvent.clientName || deletedEvent.subject || 'Unknown Client';
          // Decrement the count by 1 (using -1)
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (window as any).updateAdvisorPanelEventCount(eventClientName, -1);
          console.log('🔄 Decremented AdvisorPanel event count for:', eventClientName);
        }
      }
    } catch (error) {
      console.error('Error deleting event:', error);
      // Revert to previous state on error
      setEvents((prev: Record<string, EventType[]>) => ({ ...prev }));
      setError('Failed to delete event. Please try again.');
    } finally {
      // Reset pending delete data and close modal
      setPendingDeleteEventData(null);
      setConfirmDeleteEventModalOpen(false);
    }
  };

  // Open the edit modal for an event
  const handleEditEvent = (matterId: string, eventId: string) => {
    const eventToEdit = events[matterId]?.find(e => e.id === eventId);
    if (eventToEdit) {
      // Find the client and matter that this event belongs to
      let foundMatter: Matter | undefined;

      // Search through all clients and their matters to find the one with this matterId
      for (const client of clients) {
        const matter = client.matters.find(m => m.id === matterId);
        if (matter) {
          foundMatter = matter;
          break;
        }
      }

      // If we found the matter, set it as selectedMatter
      if (foundMatter) {
        setSelectedMatter(foundMatter);
      }

      setCurrentEditEvent(eventToEdit);
      setIsEditMode(eventToEdit.eventStatus !== 'Synced');
      setCurrentMatterId(matterId);
      setEditModalOpen(true);
    }
  };

  // Save the edited event or add a new one
  const handleSaveEditedEvent = async (matterId: string, updatedEvent: EventType) => {
    console.log('🚀 ~ handleSaveEditedEvent ~ updatedEvent:', updatedEvent);
    if (isChildWorkflow) {
      localStorage.setItem("SelectedEventMatterId", updatedEvent.client_matter_id ?? "");
      localStorage.removeItem('isEventAdded');
    }
    try {
      const matterEvents = [...(events[matterId] || [])];
      const eventIndex = matterEvents.findIndex(event => event.id === updatedEvent.id);

      // Count TBD fields and add to the event
      updatedEvent.tbdFieldsCount = countTbdFields(updatedEvent);

      // Check if this is an update or a new event
      const isNewEvent = eventIndex === -1;

      // If it's an update, replace the existing event
      if (!isNewEvent) {
        matterEvents[eventIndex] = updatedEvent;
      }
      // If it's a new event, add it to the array
      else {
        matterEvents.push(updatedEvent);
      }

      // Update local state first for immediate UI feedback
      setEvents((prev: Record<string, EventType[]>) => ({
        ...prev,
        [matterId]: matterEvents,
      }));

      // Call API to update or add event if fieldId and workflowId are available
      if (fieldId && workflowId) {
        // Start loading state
        setLoading(true);

        // Determine the API endpoint based on whether this is a new event or an update
        const apiEndpoint = isNewEvent
          ? `/workflow/court-notice/event` // POST for new events
          : `/workflow/court-notice/event`; // PUT for updates

        // Make the appropriate API call
        const apiMethod = isNewEvent ? apiPost : apiPut;

        await apiMethod(apiEndpoint, {
          field_id: fieldId,
          workflow_id: workflowId,
          matter_id: matterId,
          event: updatedEvent,
          event_id: updatedEvent.id,
        });
      }

      // Call appropriate callback based on whether this is a new event or an update
      if (isNewEvent && onAddEvent) {
        // This was an add operation
        onAddEvent(matterId);
      } else if (!isNewEvent && onEditEvent) {
        // This was an edit operation
        onEditEvent(matterId, updatedEvent.id, updatedEvent);
      }

      // Call onSave if provided
      if (onSave) {
        onSave(matterId, matterEvents, updatedEvent);
      }

      // Update AdvisorPanel event count if the global function is available
      // Only increment for new events, not updates
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      if (isNewEvent && typeof window !== 'undefined' && (window as any).updateAdvisorPanelEventCount) {
        // Use clientName first, fallback to subject if needed
        const eventClientName = updatedEvent.clientName || updatedEvent.subject || 'Unknown Event';
        if (eventClientName && eventClientName !== 'Unknown Event') {
          // Increment the count by 1 for new events
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (window as any).updateAdvisorPanelEventCount(eventClientName, 1);
          console.log('🔄 Incremented AdvisorPanel event count for new event:', eventClientName);
        }
      }
    } catch (error) {
      console.error('Error saving event:', error);
      // Revert to previous state on error
      setEvents((prev: Record<string, EventType[]>) => ({ ...prev }));
      setError('Failed to save event. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handler for adding a new event button click
  const handleAddEvent = (clientId: string, matterId: string) => {
    if (onAddEvent) {
      onAddEvent(matterId);
    } else {
      // Find the client for this matter
      const client = clients.find(c => c.id === clientId);

      if (client) {
        // If no matter is selected (empty matterId), alert the user to select one first
        if (!matterId) {
          // Show an alert or some notification that a matter must be selected
          setError('Please select a Client/Matter first');
          return;
        }

        // Find the selected matter
        const selectedMatter = client.matters.find(m => m.id === matterId);

        if (selectedMatter) {
          // Set current client and matter for the modal
          setCurrentClientId(clientId);
          setCurrentMatterId(matterId);

          // Get the client_matter_id from the selected matter (prioritize matter_id, then _id)
          const clientMatterId = selectedMatter.matter_id || selectedMatter._id || matterId;

          // Create a new event template with selected client and matter
          const newEventTemplate: EventType = {
            id: `evt-${Date.now()}`,
            caseNumber: selectedMatter?.case_number || '',
            clientName: formatClientMatterName(`${client.name} | ${selectedMatter.name}`),
            description: 'Client MUST appear',
            subject: `JT; ${client.name} (${selectedMatter.caseDescription || ''}-) ${''} `,
            courtNoticeType: '',
            appointmentAction: '',
            charge: selectedMatter.caseDescription || '',
            county: selectedMatter?.ex_county_of_arrest || '',
            courtLocation: '',
            startDate: getCurrentDateInUserTimezone(),
            endDate: getCurrentDateInUserTimezone(),
            // Convert default times from local timezone to UTC for storage
            startTime: convertTimeToUTCReliable('09:00', getCurrentDateInUserTimezone()),
            endTime: convertTimeToUTCReliable('10:00', getCurrentDateInUserTimezone()),
            allDay: false,
            requiredAttendees: '',
            optionalAttendees: '',
            clientAttendance: '',
            meetingLocation: '',
            // Add client_matter_id for attendees search to work
            client_matter_id: clientMatterId,
            selectedMatterId: matterId,
            _id: '',
          };

          // Set the event to edit and open the edit modal
          setCurrentEditEvent(newEventTemplate);
          setEditModalOpen(true);
        }
      }
    }
  };

  // Format date string for display
  const formatDate = (dateStr: string | undefined) => {
    if (!dateStr) return '';

    try {
      // Check if date is already in MM/DD/YYYY format
      if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
        return dateStr;
      }

      // Otherwise try to parse and format
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric',
      });
    } catch (e) {
      console.error('Error formatting date:', e);
      return dateStr; // If parsing fails, return original
    }
  };

  // Format time from 24-hour to AM/PM format with timezone conversion
  const formatTime = (timeStr: string | undefined, dateStr?: string) => {
    if (!timeStr) return '';

    try {
      // Convert UTC time to local timezone if dateStr is provided
      let localTimeStr = timeStr;
      if (dateStr) {
        localTimeStr = convertTimeFromUTCReliable(timeStr, dateStr);
      }

      // Handle time in HH:MM format
      const [hours, minutes] = localTimeStr.split(':');
      const hour24 = parseInt(hours, 10);
      const minute = parseInt(minutes, 10);

      if (isNaN(hour24) || isNaN(minute)) {
        return localTimeStr; // Return converted time if parsing fails
      }

      // Convert to 12-hour format
      let hour12 = hour24;
      let ampm = 'AM';

      if (hour24 === 0) {
        hour12 = 12; // Midnight case
      } else if (hour24 === 12) {
        ampm = 'PM'; // Noon case
      } else if (hour24 > 12) {
        hour12 = hour24 - 12;
        ampm = 'PM';
      }

      // Format with leading zero for minutes if needed
      const formattedMinutes = minute.toString().padStart(2, '0');

      return `${hour12}:${formattedMinutes} ${ampm}`;
    } catch (e) {
      console.error('Error formatting time:', e);
      return timeStr; // If parsing fails, return original
    }
  };

  // Handle keyboard navigation for dropdown
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, client: ClientMatter) => {
    const isDropdownOpen = openDropdownClientId === client.id;

    // Only use searchResults when dropdown is open, which now contains all matters initially or filtered results
    const matters = isDropdownOpen ? searchResults : [];

    if (!isDropdownOpen) {
      // Open dropdown on Enter, Space, or Arrow keys
      if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown' || e.key === 'ArrowUp') {
        e.preventDefault();
        setOpenDropdownClientId(client.id);
        setClientSearchTerms(prev => ({
          ...prev,
          [client.id]: '',
        }));
        setFocusedIndex(0); // Start with first item highlighted
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => {
          const nextIndex = prev < matters.length - 1 ? prev + 1 : 0;
          // Scroll highlighted option into view
          setTimeout(() => {
            const highlightedElement = document.querySelector(
              `[data-court-notice-option-index="${nextIndex}"]`
            );
            if (highlightedElement) {
              highlightedElement.scrollIntoView({
                block: 'nearest',
                behavior: 'smooth',
              });
            }
          }, 0);
          return nextIndex;
        });
        break;

      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => {
          const nextIndex = prev > 0 ? prev - 1 : matters.length - 1;
          // Scroll highlighted option into view
          setTimeout(() => {
            const highlightedElement = document.querySelector(
              `[data-court-notice-option-index="${nextIndex}"]`
            );
            if (highlightedElement) {
              highlightedElement.scrollIntoView({
                block: 'nearest',
                behavior: 'smooth',
              });
            }
          }, 0);
          return nextIndex;
        });
        break;

      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < matters.length) {
          const selectedMatter = matters[focusedIndex];
          handleMatterChange(client.id, selectedMatter.id);
          setOpenDropdownClientId(null);
          setClientSearchTerms(prev => ({
            ...prev,
            [client.id]: '',
          }));
          setSearchResults([]);
          setFocusedIndex(-1);
        }
        break;

      case 'Escape':
        e.preventDefault();
        setOpenDropdownClientId(null);
        setClientSearchTerms(prev => ({
          ...prev,
          [client.id]: '',
        }));
        setFocusedIndex(-1);
        break;
    }
  };

  // Reset focused index when dropdown opens/closes or search results change
  useEffect(() => {
    if (openDropdownClientId) {
      // Set focus to first item when dropdown opens and has results
      if (searchResults.length > 0) {
        setFocusedIndex(0);
      } else {
        setFocusedIndex(-1);
      }
    } else {
      setFocusedIndex(-1);
    }
  }, [openDropdownClientId, searchResults.length]);

  // Update focused index when search results change but dropdown remains open
  useEffect(() => {
    if (openDropdownClientId && searchResults.length > 0) {
      // If we have results and no item is focused, focus the first one
      if (focusedIndex === -1 || focusedIndex >= searchResults.length) {
        setFocusedIndex(0);
      }
    } else if (searchResults.length === 0) {
      setFocusedIndex(-1);
    }
  }, [searchResults, openDropdownClientId, focusedIndex]);

  // Add at the top-level of the component (inside CourtNotice):
  const displayedEventIdsByMatter: Record<string, Set<string>> = {};

  // Render events for a matter
  const renderMatterEvents = (clientId: string, matter: Matter) => {
    let matterEvents = events[matter.id] || [];
    // Filter out events already displayed for this matter
    if (displayedEventIdsByMatter[matter.id]) {
      matterEvents = matterEvents.filter(
        event => !displayedEventIdsByMatter[matter.id].has(event.id)
      );
      // Mark these events as displayed for subsequent boxes
      matterEvents.forEach(event => displayedEventIdsByMatter[matter.id].add(event.id));
    }
    console.log('🚀 ~ renderMatterEvents ~ matterEvents:', matterEvents);

    // Check if any events in this matter are scheduled
    const hasScheduledEvents = matterEvents.some(
      event => event.eventStatus === 'Synced' || event.eventStatus === 'SyncFailed'
    );
    const shouldDisableButtons =
      isChildWorkflow ? false : isDisabled || (isUpdateMyCase && hasScheduledEvents) || hasScheduledEvents;
    // Define shouldDisableButtons for this scope
    const shouldDisableAddEvent = isChildWorkflow ? false : hasScheduledEvents;

    return (
      <div key={matter.id} className={workflowStyles.courtNoticeEventsContainer}>
        {matterEvents.length > 0 && <div className="font-medium text-[#2A2E34] mb-2">Events</div>}

        {/* Events list */}
        {matterEvents.length > 0 ? (
          matterEvents.map(event => {
            // Handle rescheduled events - this should be done outside of render
            // We'll create a separate effect to handle this

            // Calculate TBD fields count if not already set
            const tbdCount = event.tbdFieldsCount ?? countTbdFields(event);
            console.log('🚀 ~ renderMatterEvents ~ event:', event);

            // Check if this event should have delete button disabled
            const shouldDisableDelete = isChildWorkflow ? false :
              isDisabled ||
              (isUpdateMyCase && event.eventStatus === 'Synced') ||
              event.eventStatus === 'Synced' ||
              event.eventStatus === 'SyncFailed';

            const shouldDisableDeleteNew = isChildWorkflow ? false :
              isDisabled ||
              (isUpdateMyCase && event.eventStatus === 'Synced') ||
              event.eventStatus === 'Synced' ||
              event.eventStatus === 'SyncFailed';

            const shouldDisableEdit = isChildWorkflow ? false :
              isTaskReviewed ||
              event.eventStatus === 'Synced' ||
              event.eventStatus === 'SyncFailed';

            // setIsEditPage(shouldDisableEdit)

            console.log('==============eventsubject ****========', event.subject);

            return (
              <div key={event.id} className={workflowStyles.courtNoticeEvent}>
                <div className="flex items-start justify-between">
                  {event?.subject !== 'Save court notice only' ? (
                    <div className={workflowStyles.courtNoticeEventDetails}>
                      <div className={workflowStyles.courtNoticeEventClientName}>
                        {/* {event?.appointmentAction && (
                          <span className="mr-2 px-2 py-1 text-xs font-medium bg-blue-100 text-[#3F73F6] rounded-[12px]">
                            {event.appointmentAction}
                          </span>
                        )} */}
                        {/* {event?.appointmentAction && (
                          <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            {event.appointmentAction}
                          </span>
                        )} */}
                        {event?.subject}
                      </div>
                      {/* <div className={workflowStyles.courtNoticeEventCaseNumber}>{event.caseNumber}</div> */}
                      {
                        <div className={workflowStyles.courtNoticeEventTiming}>
                          {event?.appointmentAction && (
                            <span className="px-[12px] py-[4px] mr-2 text-[12px] bg-[#C7D1DF] text-[#2A2E34] rounded-[100px]">
                              {event.appointmentAction}
                            </span>
                          )}
                          {/* {formatDate(event.startDate || event.date)} {formatTime(event.startTime, event.startDate || event.date)}{' '}
                          - {formatTime(event.endTime, event.endDate || event.startDate || event.date)} */}
                          {formatDate(event.startDate || event.date)} {formatTime(event.startTime, event.startDate || event.date)}{' '}
                          {' to '}
                          {formatDate(event.endDate)} {formatTime(event.endTime, event.endDate || event.startDate || event.date)}
                        </div>
                      }
                    </div>
                  ) : (
                    <>
                      <div className={workflowStyles.courtNoticeEventDetails}>
                        <div className={workflowStyles.courtNoticeEventClientName}>
                          {event?.subject}{' '}
                        </div>
                      </div>
                    </>
                  )}

                  <div className={workflowStyles.courtNoticeActionButtons}>
                    <button
                      onClick={() => toggleEventStatus(matter.id, event.id)}
                      className={
                        event.isCompleted
                          ? workflowStyles.courtNoticeButtonCompleted
                          : workflowStyles.courtNoticeButtonPending
                      }
                      title={event.isCompleted ? '' : `${tbdCount > 0 ? ` ` : ''}`}
                      disabled={shouldDisableDelete}
                    >
                      {tbdCount > 0 ? (
                        <div className="bg-[#F6B175] text-white rounded-full w-[20px] h-[20px] flex items-center justify-center text-xs font-medium leading-none">
                          {tbdCount}
                        </div>
                      ) : (
                        <Image
                          src="/assets/check-circle-filled.svg"
                          alt="Check"
                          width={20}
                          height={20}
                        />
                      )}
                    </button>
                    {/* Delete button visibility logic */}
                    {(!isChildWorkflow ||
                      (isChildWorkflow &&
                        // For child workflow, show delete button only if:
                        // 1. eventStatus is "New" AND (isAddSecondary is undefined OR false)
                        event.eventStatus === 'New' &&
                        (!event.isAddSecondary || event.isAddSecondary === undefined))) && (
                        <button
                          onClick={() => removeEvent(matter.id, event.id)}
                          className={workflowStyles.courtNoticeButtonPending + ' hover:text-red-500'}
                          title="Delete event"
                          disabled={
                            !isChildWorkflow
                              ? shouldDisableDeleteNew
                              : event.eventStatus === 'Synced' ||
                              event.eventStatus === 'SyncFailed' ||
                              (event.eventStatus === 'New' && event.isAddSecondary === true)
                          }
                        >
                          {(
                            !isChildWorkflow
                              ? shouldDisableDeleteNew
                              : event.eventStatus === 'Synced' ||
                              event.eventStatus === 'SyncFailed' ||
                              (event.eventStatus === 'New' && event.isAddSecondary === true)
                          ) ? (
                            <Image
                              src="/assets/trash-03-gray.svg"
                              alt="Delete"
                              width={20}
                              height={20}
                            />
                          ) : (
                            <Image src="/assets/trash-03.svg" alt="Delete" width={20} height={20} />
                          )}
                        </button>
                      )}
                    {!shouldDisableEdit || isTaskReviewed ? (
                      <button
                        onClick={() => handleEditEvent(matter.id, event.id)}
                        className={workflowStyles.courtNoticeButtonPending + ' hover:text-blue-500'}
                        title={isTaskReviewed ? "View event" : "Edit event"}
                        disabled={shouldDisableEdit && !isTaskReviewed}
                      >
                        {isTaskReviewed ? (
                          <Image src="/assets/eyeview.svg" alt="View" width={20} height={20} />
                        ) : shouldDisableEdit ? (
                          <Image src="/assets/edit-gray.svg" alt="Edit" width={20} height={20} />
                        ) : (
                          <Image src="/assets/edit.svg" alt="Edit" width={20} height={20} />
                        )}
                      </button>
                    ) : (
                      <button
                        onClick={() => handleEditEvent(matter.id, event.id)}
                        title="View event"
                      >
                        <Image src="/assets/eyeview.svg" alt="View" width={20} height={20} />
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <></>
        )}

        {/* Action buttons */}
        <div className="flex w-full gap-4">
          <button
            className={`flex items-center font-medium justify-center h-[36px] text-[#3F73F6] text-[14px] border border-[#3F73F6] rounded-[4px] py-[12px] px-[16px] w-full ${conditionalDisabled(isTaskReviewed || isChildWorkflow || shouldDisableAddEvent, 'button', 'cursor-pointer')}`}
            onClick={() => handleAddEvent(clientId, matter.id)}
            disabled={isTaskReviewed || shouldDisableAddEvent || isChildWorkflow}
          >
            <span className="mr-2 text-xl">+</span>
            Add a new event
          </button>
          <button
            className={`flex items-center justify-center h-[36px] text-[14px] gap-2 font-medium text-[#3F73F6] border border-[#3F73F6] rounded-md py-3 px-[16px] w-full ${conditionalDisabled(allDisableButtons, 'button', 'cursor-pointer')}`}
            disabled={allDisableButtons}
          >
            All events
            {allDisableButtons ? (
              <Image
                src="/assets/Vector-right-gray.svg"
                alt="View all"
                className={conditionalDisabled(shouldDisableButtons, 'icon')}
                width={12}
                height={12}
              />
            ) : (
              <Image
                src="/assets/Vector-right.svg"
                alt="View all"
                className={conditionalDisabled(shouldDisableButtons, 'icon')}
                width={12}
                height={12}
              />
            )}
          </button>
        </div>
      </div>
    );
  };

  // Render a client section - updated for client-specific search terms
  const renderClientSection = (client: ClientMatter) => {
    console.log("Mutlple time");
    const selectedMatterId = selectedMatters[client.id] || '';
    const selectedMatter = client.matters.find(m => m.id === selectedMatterId);
    console.log('🚀 ~ renderClientSection ~ selectedMatter:', selectedMatter);
    const isDropdownOpen = openDropdownClientId === client.id;
    const clientSearchTerm = clientSearchTerms[client.id] || '';

    // Show search results when available, otherwise show empty array
    // The search results will contain all matters initially or filtered results when searching
    const displayedMatters = isDropdownOpen ? searchResults : [];

    // Check if any events in this client's matters are scheduled
    const hasScheduledEvents = client.matters.some(matter => {
      const matterEvents = events[matter.id] || [];
      return matterEvents.some(
        event => event.eventStatus === 'Synced' || event.eventStatus === 'SyncFailed'
      );
    });

    const shouldDisableDelete = isChildWorkflow ? false :
      isDisabled || (isUpdateMyCase && hasScheduledEvents) || hasScheduledEvents;

    // Define shouldDisableButtons for this scope
    const shouldDisableButtons = isChildWorkflow ? false :
      isDisabled || (isUpdateMyCase && hasScheduledEvents) || hasScheduledEvents;

    if (!displayedEventIdsByMatter[selectedMatterId]) {
      displayedEventIdsByMatter[selectedMatterId] = new Set();
    }

    return (
      <div key={client.id} className={workflowStyles.courtNoticeCard}>
        {/* Client Header */}
        {!isChildWorkflow && (
          <div className={workflowStyles.courtNoticeClientHeader}>
            <div className="flex justify-between  items-center">
              <button className="flex items-center font-medium text-left w-full">
                {/* Client name is shown in the dropdown now */}
              </button>
              <button
                onClick={() => removeClientCard(client.id)}
                className="text-gray-400 hover:text-gray-600"
                disabled={shouldDisableDelete}
              >
                {shouldDisableDelete ? (
                  <Image src="/assets/x-close.svg" alt="Delete" width={20} height={20} />
                ) : (
                  <X size={20} className="text-[#5F6F84]" />
                )}
              </button>
            </div>

            {/* Client's matters dropdown - Searchable version */}
            <div>
              <label className="block text-[14px] font-medium text-[#2A2E34] mb-1">
                Client/Matter
              </label>
              <div className="relative mt-2">
                <input
                  type="text"
                  className={`
                    w-full py-[12px] px-[14px] h-[36px] border font-normal rounded-[4px] text-[#2A2E34] bg-white flex items-center flex-wrap gap-1
                    focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-200 focus-within:border-[#3F73F6]
                    disabled:cursor-not-allowed disabled:bg-[#F3F5F9] disabled:text-[#A2AFC2] disabled:border-[#DCE2EB]
                    ${isDropdownOpen ? 'border-[#3F73F6] ring-1 ring-[#3F73F6]' : 'border-[#C7D1DF]'}
                  `}
                  placeholder="Select Client/Matter"
                  value={
                    clientSearchTerm ||
                    (selectedMatter && !isDropdownOpen
                      ? formatClientMatterName(selectedMatter.name)
                      : '')
                  }
                  onChange={e => {
                    setClientSearchTerms(prev => ({
                      ...prev,
                      [client.id]: e.target.value,
                    }));
                    if (openDropdownClientId !== client.id) {
                      setOpenDropdownClientId(client.id);
                    }
                  }}
                  onFocus={() => {
                    if (!isDisabled) {
                      setOpenDropdownClientId(client.id);
                      setClientSearchTerms(prev => ({
                        ...prev,
                        [client.id]: '',
                      }));
                    }
                  }}
                  onClick={() => {
                    if (!isDisabled) {
                      setOpenDropdownClientId(client.id);
                      setClientSearchTerms(prev => ({
                        ...prev,
                        [client.id]: '',
                      }));
                    }
                  }}
                  onKeyDown={e => handleKeyDown(e, client)}
                  disabled={shouldDisableDelete}
                  autoComplete="off"
                  role="combobox"
                  aria-haspopup="listbox"
                  aria-expanded={isDropdownOpen}
                  aria-activedescendant={
                    isDropdownOpen && focusedIndex >= 0
                      ? `court-notice-option-${client.id}-${focusedIndex}`
                      : undefined
                  }
                />

                {/* Chevron icon with rotation */}
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3">
                  <ChevronDown
                    size={20}
                    className={`transition-transform duration-300 ${isDropdownOpen ? 'rotate-180 text-[#3F73F6]' : 'rotate-0'} ${conditionalDisabled(shouldDisableDelete, 'icon', 'text-[#5F6F84]')}`}
                  />
                </div>

                {/* Dropdown styling */}
                {isDropdownOpen && (
                  <div
                    ref={dropdownRef}
                    className="absolute z-50 w-full mt-1 max-h-[150px] overflow-y-scroll bg-white border border-[#C7D1DF] rounded-[8px] shadow-lg animate-fadeIn overflow-hidden"
                    role="listbox"
                  >
                    <div className="max-h-60">
                      {isSearching ? (
                        <div className="p-4 text-center text-gray-500">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#3F73F6] mx-auto mb-2"></div>
                          Searching...
                        </div>
                      ) : displayedMatters.length > 0 ? (
                        displayedMatters
                          .filter(matter => !Object.values(selectedMatters).includes(matter.id))
                          .map((matter, index) => (
                            <div
                              key={`${client.id}-${matter.id}`}
                              className={`p-3 hover:bg-[#F8F9FC] cursor-pointer transition-colors duration-150 ${focusedIndex === index
                                ? 'bg-[#F1F5F9] text-[#2A2E34]'
                                : 'text-[#2A2E34] hover:bg-[#F8F9FC]'
                                }`}
                              onClick={() => {
                                handleMatterChange(client.id, matter.id);
                                setOpenDropdownClientId(null);
                                setClientSearchTerms(prev => ({
                                  ...prev,
                                  [client.id]: '',
                                }));
                                setSearchResults([]);
                              }}
                              onMouseEnter={() => setFocusedIndex(index)}
                            >
                              {formatClientMatterName(matter.name)}
                            </div>
                          ))
                      ) : clientSearchTerm && clientSearchTerm.length >= 2 ? (
                        <div className="p-4 text-center text-[#5F6F84]">No matches found</div>
                      ) : (
                        <div className="p-4 text-center text-[#5F6F84]">No matters available</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Only show Add New Event buttons if a matter is selected, otherwise show prompt */}
        {selectedMatter ? (
          renderMatterEvents(client.id, selectedMatter)
        ) : (
          <div className="p-4 text-center">
            <div className="flex w-full gap-4">
              <button
                className={`flex items-center font-medium justify-center h-[36px] text-[#3F73F6] text-[14px] border border-[#3F73F6] rounded-[4px] py-[12px] px-[16px] w-full ${conditionalDisabled(shouldDisableButtons, 'button', 'cursor-pointer')}`}
                onClick={() => setError('Please select a Client/Matter first')}
                disabled={isChildWorkflow ? false : isDisabled}
              >
                <span className="mr-2 text-xl">+</span>
                Add a new event
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Remove a client's contact card
  const removeClientCard = async (clientId: string) => {
    // Log the client being removed for debugging
    // console.log('Attempting to remove client with ID dhyey:', clientId);
    // console.log('Current clients before removal: dhyey', clients);

    // Set pending delete client ID and show confirmation modal instead of using confirm()
    setPendingDeleteClientId(clientId);
    setConfirmDeleteClientModalOpen(true);
  };

  // Handle confirmed client deletion
  const handleConfirmClientDelete = async () => {
    if (!pendingDeleteClientId) return;

    const clientId = pendingDeleteClientId;
    // console.log('Confirming deletion of client with dhyey ID:', clientId);

    try {
      const clientToRemove = clients.find(c => c.id === clientId);

      // console.log('🚀 ~ handleConfirmClientDelete ~ dhyey clientToRemove:', clientToRemove);

      if (!clientToRemove) {
        console.error('Client to remove not found:', clientId);
        return;
      }

      console.log('Found client to remove:', clientToRemove);

      // Update local state to remove only the specific client with this ID
      setClients((prevClients: ClientMatter[]) => {
        const remainingClients = prevClients.filter(client => client.id !== clientId);
        console.log('Clients after removal:', remainingClients);
        return remainingClients;
      });

      // Only remove events related to this specific client's matters
      const matterIdsToRemove = new Set(clientToRemove.matters.map(matter => matter.id));
      console.log('Matter IDs to remove events for:', Array.from(matterIdsToRemove));

      setEvents((prevEvents: Record<string, EventType[]>) => {
        const updatedEvents = { ...prevEvents };

        // Only remove events for matters belonging to this client
        Array.from(matterIdsToRemove).forEach(matterId => {
          delete updatedEvents[matterId];
        });

        return updatedEvents;
      });

      // Clean up related state for this specific client
      setSelectedMatters(prev => {
        const newState = { ...prev };
        delete newState[clientId];
        return newState;
      });

      // Also clean up client-specific search terms
      setClientSearchTerms(prev => {
        const newState = { ...prev };
        delete newState[clientId];
        return newState;
      });

      // console.log('🚀 ~ handleConfirmClientDelete ~ dhyey clientId:', clientId);
      // Call the parent component's onDeleteClient handler if provided
      // console.log('🚀 ~ handleConfirmClientDelete ~ dhyey onDeleteClient:', clientId);
      if (onDeleteClient) {
        onDeleteClient(clientId);
      }
      // Otherwise handle API call directly if fieldId and workflowId are available
      else if (fieldId && workflowId) {
        await apiDelete(`/workflow/court-notice/client`, {
          data: {
            field_id: fieldId,
            workflow_id: workflowId,
            client_id: clientId,
          },
        });
      }

      // Force re-render to ensure UI is up-to-date
      setLastContactAdded(Date.now());
    } catch (error) {
      console.error('Error removing client:', error);
      // Revert to previous state on error
      setError('Failed to remove client. Please try again.');
    } finally {
      // Reset pending delete client ID and close modal
      setPendingDeleteClientId(null);
      setConfirmDeleteClientModalOpen(false);
    }
  };

  // Add event to another contact button
  const renderAddContactButton = () => {
    // Check if any events across all clients are scheduled
    const hasAnyScheduledEvents = Object.values(events).some(matterEvents =>
      matterEvents.some(
        event => event.eventStatus === 'Synced' || event.eventStatus === 'SyncFailed'
      )
    );

    const shouldDisableAddContact = isChildWorkflow ? false :
      isDisabled ||
      (isUpdateMyCase && hasAnyScheduledEvents) ||
      hasAnyScheduledEvents;

    return (
      <button
        className={`${workflowStyles.courtNoticeAddButton} ${conditionalDisabled(shouldDisableAddContact, 'button', 'cursor-pointer')}`}
        disabled={shouldDisableAddContact}
        onClick={async () => {
          if (onAddContact) {
            console.log('🚀 ~ renderAddContactButton ~ dhyey onAddContact:', onAddContact);
            // First call the onAddContact function to open modal or handle UI
            onAddContact(true);
            // If we're in a workflow context, also make an API call
            try {
              if (fieldId && workflowId) {
                // Start loading state
                setLoading(true);

                // Add the newClient flag to indicate this client was added via "Add event to another contact"
                const response = await apiPost<{
                  statusCode: number;
                  data?: {
                    clients?: ClientMatter[];
                    events?: Record<string, EventType[]>;
                  };
                }>(`/workflow/court-notice/add-contact`, {
                  field_id: fieldId,
                  workflow_id: workflowId,
                  newClient: true, // Add this flag to mark as a new client from "Add event to another contact"
                });

                // If the API returns new client data, update the local state
                if (response.data?.data?.clients) {
                  // Mark any new clients with newClient: true if not already set
                  const newClients = response.data.data.clients.map((client: ClientMatter) => ({
                    ...client,
                    newClient: client.newClient === undefined ? true : client.newClient,
                  }));

                  // Special handling for the first contact being added
                  if (!firstContactAdded) {
                    // First time adding a contact, just set the clients directly
                    setClients(newClients);
                    setFirstContactAdded(true);
                    console.log('First contact added, setting clients directly:', newClients);
                  } else {
                    // For subsequent additions, merge with existing clients
                    setClients((prevClients: ClientMatter[]) => {
                      // Create map of existing client IDs to avoid duplicates
                      const existingClientIds = new Set(prevClients.map((c: ClientMatter) => c.id));

                      // Add only new clients that don't already exist
                      const clientsToAdd = newClients.filter(
                        (client: ClientMatter) => !existingClientIds.has(client.id)
                      );

                      console.log('Subsequent contact added, merging with existing clients:', {
                        existingCount: prevClients.length,
                        newCount: clientsToAdd.length,
                      });

                      // Return combined array with existing clients first, followed by new ones
                      return [...prevClients, ...clientsToAdd];
                    });
                  }
                }

                if (response.data?.data?.events) {
                  const newEvents = response.data?.data?.events || {};

                  // Special handling for first contact being added
                  if (!firstContactAdded) {
                    // First time adding a contact, directly set the events
                    setEvents(newEvents);
                    console.log('First contact events directly set:', newEvents);
                  } else {
                    // For subsequent additions, merge with existing events
                    setEvents((prevEvents: Record<string, EventType[]>) => {
                      // Create a merged events object
                      const mergedEvents = { ...prevEvents };

                      // Add or merge events for each matter ID
                      Object.keys(newEvents).forEach((matterId: string) => {
                        if (!mergedEvents[matterId]) {
                          // If no events exist for this matter, add them all
                          mergedEvents[matterId] = [...newEvents[matterId]];
                        } else {
                          // If events exist, add only new ones (avoiding duplicates by ID)
                          const existingEventIds = new Set(
                            mergedEvents[matterId].map((event: EventType) => event.id)
                          );
                          const eventsToAdd = newEvents[matterId].filter(
                            (event: EventType) => !existingEventIds.has(event.id)
                          );
                          mergedEvents[matterId] = [...mergedEvents[matterId], ...eventsToAdd];
                        }
                      });

                      console.log('Subsequent contact events merged:', {
                        prevMatterCount: Object.keys(prevEvents).length,
                        newMatterCount: Object.keys(newEvents).length,
                        resultMatterCount: Object.keys(mergedEvents).length,
                      });

                      return mergedEvents;
                    });
                  }
                }

                // Update lastContactAdded to force rerender
                setLastContactAdded(Date.now());
              }
            } catch (error) {
              console.error('Error adding contact:', error);
              setError('Failed to add new contact. Please try again.');
            } finally {
              setLoading(false);
            }
          }
        }}
      >
        <Plus
          size={20}
          className={`mr-2 ${conditionalDisabled(shouldDisableAddContact, 'icon')}`}
        />
        <p
          className={`text-[14px] font-medium leading-[20px] text-[#3F73F6] ${conditionalDisabled(shouldDisableAddContact, 'banner')}`}
        >
          Add event to another contact
        </p>
      </button>
    );
  };

  // Confirm and execute matter deletion
  const handleConfirmMatterDelete = async () => {
    if (!pendingDeleteMatterId) return;

    try {
      setLoading(true);

      // Use the provided callback to handle deletion in parent
      if (onDeleteMatter) {
        onDeleteMatter(pendingDeleteMatterId);
      } else {
        // Handle deletion locally if no callback provided
        const updatedEvents = { ...events };
        delete updatedEvents[pendingDeleteMatterId];
        setEvents(updatedEvents);
      }

      // Reset state
      setPendingDeleteMatterId(null);
      setConfirmDeleteMatterModalOpen(false);
    } catch (error) {
      console.error('Error deleting matter:', error);
      setError('Failed to delete matter. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className={workflowStyles.courtNoticeContainer}>
        {/* <div className={workflowStyles.courtNoticeHeader}>
          <span className={workflowStyles.courtNoticeHeaderText}>{label}</span>
        </div> */}
        <div className="text-center p-8 border border-gray-200 rounded-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <div className="text-gray-500">Loading court notice data...</div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={workflowStyles.courtNoticeContainer}>
        {/* <div className={workflowStyles.courtNoticeHeader}>
          <span className={workflowStyles.courtNoticeHeaderText}>{label}</span>
        </div> */}
        <div className="text-center p-8 border border-gray-200 rounded-lg bg-red-50">
          <AlertTriangle size={24} className="text-red-500 mx-auto mb-2" />
          <div className="text-red-500 mb-2">{error}</div>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md"
            onClick={() => setError(null)}
          >
            Dismiss
          </button>
        </div>
      </div>
    );
  }

  // If no clients are available
  if (clients.length === 0) {
    return (
      <div className={workflowStyles.courtNoticeContainer}>
        {/* <div className={workflowStyles.courtNoticeHeader}>
          <span className={workflowStyles.courtNoticeHeaderText}>{label}</span>
        </div> */}
        <div className="text-center p-8 border border-gray-200 rounded-lg">
          <Calendar size={24} className="text-gray-400 mx-auto mb-2" />
          <div className="text-gray-500 mb-4">Start by clicking on the button below</div>
          {onAddContact && renderAddContactButton()}
        </div>
      </div>
    );
  }

  // Main component render (using lastContactAdded to help with forcing rerenders)
  return (
    <div className={workflowStyles.courtNoticeContainer} key={`container-${lastContactAdded}`}>
      {/* Client sections */}
      {clients.map(client => renderClientSection(client))}

      {/* Add event to another contact button - only show if onAddContact is provided */}
      {onAddContact && renderAddContactButton()}

      {/* Edit/Add modal */}
      <CourtNoticeEditModal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        event={currentEditEvent}
        clientId={currentMatterId}
        onSave={handleSaveEditedEvent}
        options={localOptions}
        isDisabled={isDisabled}
        workflowId={workflowId}
        titleOfEvent={'Edit event'}
        isEditMode={isEditMode}
        isChildWorkflow={isChildWorkflow}
        clientName={selectedMatterClientNames[currentMatterId] || ''}
        isTaskReviewed={isTaskReviewed}
        selectedMatter={selectedMatter}
      />

      {/* Confirm Delete Event Modal */}
      <ReusableAlertModal
        isOpen={confirmDeleteEventModalOpen}
        title="Confirm Delete"
        message="Are you sure you want to remove this event?"
        onOk={handleConfirmEventDelete}
        onCancel={() => {
          setPendingDeleteEventData(null);
          setConfirmDeleteEventModalOpen(false);
        }}
        okText="Delete"
        cancelText="Cancel"
      />

      {/* Confirm Delete Client Modal */}
      <ReusableAlertModal
        isOpen={confirmDeleteClientModalOpen}
        title="Confirm Delete"
        message="Are you sure you want to remove this client?"
        onOk={handleConfirmClientDelete}
        onCancel={() => {
          setPendingDeleteClientId(null);
          setConfirmDeleteClientModalOpen(false);
        }}
        okText="Delete"
        cancelText="Cancel"
      />

      {/* Confirm Delete Matter Modal */}
      <ReusableAlertModal
        isOpen={confirmDeleteMatterModalOpen}
        title="Confirm Delete"
        message="Are you sure you want to remove this matter?"
        onOk={handleConfirmMatterDelete}
        onCancel={() => {
          setPendingDeleteMatterId(null);
          setConfirmDeleteMatterModalOpen(false);
        }}
        okText="Delete"
        cancelText="Cancel"
      />
    </div>
  );
};

export default CourtNotice;

// Timezone utility functions
const getUserTimezone = (): string => {
  if (typeof window === 'undefined') return 'UTC';
  return localStorage.getItem('timezone') || Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
};

// Convert time from UTC to user timezone for display
const convertTimeFromUTCReliable = (utcTimeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!utcTimeString || !dateString) return utcTimeString;

    const timezone = userTimezone || getUserTimezone();

    // Create UTC datetime
    const utcDateTime = new Date(`${dateString}T${utcTimeString}:00Z`);

    // Convert to user's timezone
    const userDateTime = new Date(utcDateTime.toLocaleString('en-US', { timeZone: timezone }));

    const localHours = userDateTime.getHours().toString().padStart(2, '0');
    const localMinutes = userDateTime.getMinutes().toString().padStart(2, '0');

    return `${localHours}:${localMinutes}`;
  } catch (error) {
    console.error('Error converting time from UTC (reliable):', error);
    return utcTimeString;
  }
};

const getCurrentDateInUserTimezone = (timezone?: string): string => {
  const userTimezone = timezone || getUserTimezone();
  const now = new Date();

  // Create a date object in the user's timezone
  const userDate = new Date(now.toLocaleString('en-US', { timeZone: userTimezone }));

  // Format as YYYY-MM-DD
  const year = userDate.getFullYear();
  const month = String(userDate.getMonth() + 1).padStart(2, '0');
  const day = String(userDate.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

// Convert time from user timezone to UTC for storage
const convertTimeToUTCReliable = (timeString: string, dateString: string, userTimezone?: string): string => {
  try {
    if (!timeString || !dateString) return timeString;

    const timezone = userTimezone || getUserTimezone();

    // Create date in user's timezone
    const dateTime = new Date(`${dateString}T${timeString}:00`);

    // Get the timezone offset for this specific date/time
    const userDate = new Date(dateTime.toLocaleString('en-US', { timeZone: timezone }));
    const utcDate = new Date(dateTime.toLocaleString('en-US', { timeZone: 'UTC' }));
    const offset = userDate.getTime() - utcDate.getTime();

    // Apply offset to get UTC time
    const utcDateTime = new Date(dateTime.getTime() - offset);

    const utcHours = utcDateTime.getHours().toString().padStart(2, '0');
    const utcMinutes = utcDateTime.getMinutes().toString().padStart(2, '0');

    return `${utcHours}:${utcMinutes}`;
  } catch (error) {
    console.error('Error converting time to UTC (reliable):', error);
    return timeString;
  }
};

// Convert time from UTC to user timezone for display
