import React from 'react';
import { RefreshCcw, User, Save } from 'lucide-react';
import { workflowStyles } from '@/styles/workflow';
import WorkflowField from '../fields/WorkflowField';
import { Task, FormDataType, TaskAction } from '@/types/workflow';

interface TaskFormProps {
  task: Task;
  formData: FormDataType;
  saving: boolean;
  saveSuccess: boolean;
  saveError: string | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onInputChange: (fieldId: string, value: any) => void;
  onTaskAction: (action: TaskAction) => void;
}

/**
 * TaskForm Component
 * Renders the form for the selected workflow task
 */
const TaskForm: React.FC<TaskFormProps> = ({
  task,
  formData,
  saving,
  saveSuccess,
  saveError,
  onInputChange,
  onTaskAction,
}) => {
  return (
    <div className="flex-1 overflow-y-auto">
      <div className="p-4">
        <h1 className="text-xl font-medium mb-4">{task.name}</h1>

        <div className={workflowStyles.container}>
          {/* Header section */}
          <div className={workflowStyles.headerSection}>
            <div className={workflowStyles.assignToSection}>
              <User className={workflowStyles.assignIcon} />
              <span>Assign To</span>
            </div>
            <div className={workflowStyles.dueDateSection}>
              <RefreshCcw className={workflowStyles.dueDateIcon} />
              <span>Due Date</span>
            </div>
          </div>

          {/* Form fields */}
          {task.formFields.map(section => (
            <WorkflowField
              key={`section-${section.id}`}
              section={section}
              onInputChange={onInputChange}
              formData={formData}
            />
          ))}

          {/* Save status messages */}
          {saveSuccess && (
            <div className="mt-4 p-2 bg-green-100 text-green-700 rounded-[12px]">
              Data saved successfully!
            </div>
          )}

          {saveError && (
            <div className="mt-4 p-2 bg-red-100 text-red-700 rounded-[12px]">
              Error saving data: {saveError}
            </div>
          )}
        </div>

        <div className={workflowStyles.buttonContainer}>
          <button className={workflowStyles.buttonSecondary} onClick={() => onTaskAction('skip')}>
            Skip
          </button>
          <button
            className={`${workflowStyles.buttonSecondary} ml-2`}
            onClick={() => onTaskAction('save')}
            disabled={saving}
          >
            {saving ? (
              <span className="flex items-center">
                <RefreshCcw className="animate-spin mr-2" size={16} />
                Saving...
              </span>
            ) : (
              <span className="flex items-center">
                <Save className="mr-2" size={16} />
                Save
              </span>
            )}
          </button>
          <button className={workflowStyles.buttonPrimary} onClick={() => onTaskAction('complete')}>
            Complete
          </button>
        </div>
      </div>
    </div>
  );
};

export default TaskForm;
