import React, { ReactNode } from 'react';
// import { useRouter } from 'next/router';
import Header from '@/components/layout/Header';
import WorkflowSidebar from '../sidebar/WorkflowSidebar';

interface WorkflowLayoutProps {
  children: ReactNode;
}

const WorkflowLayout: React.FC<WorkflowLayoutProps> = ({ children }) => {
  // const router = useRouter();

  return (
    <div className="flex h-screen overflow-hidden bg-white">
      <WorkflowSidebar />
      <div className="flex flex-col flex-1 overflow-hidden">
        <Header />
        <main className="flex-1 overflow-y-auto">{children}</main>
      </div>
    </div>
  );
};

export default WorkflowLayout;
