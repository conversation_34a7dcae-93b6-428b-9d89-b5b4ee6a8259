import React, { useState, useEffect } from 'react';
import { courtNoticeService } from '@/services/api';
import { CourtNoticeItem, CourtNoticeListParams, mapStatusVariant } from '@/types/courtNotice';
import { useRouter } from 'next/router';
import Image from 'next/image';
import PageHeader from '@/components/common/PageHeader';
import DataTable from '@/components/common/DataTable';
import Pagination from '@/components/common/Pagination';
import SaveViewModal from '@/components/common/SaveViewModal';
import StatusBadge from '@/components/common/StatusBadge';
import { UserView, UserViewService } from '@/services/userViewService';

interface CourtNoticeArchivedProps {
  savedView?: UserView;
  viewTitle?: string;
}

interface Column {
  id: string;
  header: string;
  width?: string;
  sortable?: boolean;
  sortField?: string;
  cell: (row: CourtNoticeItem) => React.ReactNode;
}

const getInitials = (name: string): string => {
  return name
    ?.split(' ')
    ?.map(word => word[0])
    ?.join('')
    ?.toUpperCase()
    ?.slice(0, 2);
};

const CourtNoticeArchived: React.FC<CourtNoticeArchivedProps> = ({
  savedView,
  viewTitle = 'Court Notice',
}) => {
  const router = useRouter();
  const [workflows, setWorkflows] = useState<CourtNoticeItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);
  const [sortBy, setSortBy] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0 });

  useEffect(() => {
    // Apply saved view filters if available
    if (savedView?.filters) {
      const filters = savedView.filters;
      if (filters.search && typeof filters.search === 'string') setSearchTerm(filters.search);
      if (filters.sortBy && typeof filters.sortBy === 'string') setSortBy(filters.sortBy);
      if (
        filters.sortOrder &&
        typeof filters.sortOrder === 'string' &&
        (filters.sortOrder === 'desc' || filters.sortOrder === 'asc')
      )
        setSortOrder(filters.sortOrder);
    }
  }, [savedView]);

  useEffect(() => {
    fetchCourtNoticeList();
  }, [pagination.page, pagination.limit, sortBy, sortOrder]);

  // Search effect with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (pagination.page === 1) {
        fetchCourtNoticeList();
      } else {
        // Reset to page 1 when search term changes
        setPagination({ ...pagination, page: 1 });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const fetchCourtNoticeList = async () => {
    try {
      setLoading(true);
      setError(null);
      localStorage.setItem('workflowShowAllToggle', String(false));

      const params: CourtNoticeListParams = {
        page: pagination.page,
        limit: pagination.limit,
        type: 'archive',
        userId: '',
      };

      if (searchTerm) {
        params.search = searchTerm;
      }

      if (sortBy) {
        params.sortBy = sortBy;
        params.sortOrder = sortOrder;
      }

      const response = await courtNoticeService.getCourtNoticeList(params);

      if (response.statusCode === 200) {
        setWorkflows(response.data.fields);
        setPagination({
          ...pagination,
          total: response.data.total,
        });
      } else {
        setError('Failed to fetch court notice data');
      }
    } catch (err) {
      console.error('Error fetching court notice data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectWorkflow = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(prev => [...prev, id]);
    } else {
      setSelectedWorkflows(prev => prev.filter(itemId => itemId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(workflows.map(item => item.id.toString()));
    } else {
      setSelectedWorkflows([]);
    }
  };

  const handleFilter = () => {
    console.log('Filter button clicked');
  };

  const handleColumns = () => {
    console.log('Columns button clicked');
  };

  const handleSaveView = () => {
    setShowSaveViewModal(true);
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      const viewData = {
        name: viewName,
        type: 'archive',
        userId: 1,
        filters: {
          search: searchTerm,
          sortBy,
          sortOrder,
        },
        columns: columns.map(col => ({
          FieldName: col.header,
          searchable: true,
          sortable: col.sortable || false,
          visible: true,
        })),
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        console.log('View saved successfully:', response.data.view);
        // Refresh the sidebar views
        const refreshSidebarViews = (window as unknown as { refreshSidebarViews?: () => void })
          .refreshSidebarViews;
        if (refreshSidebarViews) {
          refreshSidebarViews();
        }
      } else {
        console.error('Failed to save view:', response.data.message);
      }
    } catch (error) {
      console.error('Error saving view:', error);
    } finally {
      setSavingView(false);
      setShowSaveViewModal(false);
    }
  };

  const handleRowClick = (rowData: CourtNoticeItem) => {
    router.push(`/workflowrun?taskId=${rowData.last_task_id}&work_flow_id=${rowData._id}`);
  };

  const handleSort = (field: string, order: 'asc' | 'desc') => {
    setSortBy(field);
    setSortOrder(order);
    setPagination({ ...pagination, page: 1 });
  };

  const handlePageChange = (page: number) => {
    setPagination({ ...pagination, page });
  };

  const handleItemsPerPageChange = (limit: number) => {
    setPagination({ page: 1, limit, total: pagination.total });
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  const columns: Column[] = [
    {
      id: 'workflowRun',
      header: 'WORKFLOW RUN',
      width: '200px',
      sortable: true,
      sortField: 'work_flow_runner',
      cell: (row: CourtNoticeItem) => (
        <div
          className="text-[14px] text-[#2A2E34] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {row.work_flow_runner.split(',').length > 2
            ? row.work_flow_runner.split(',').slice(0, 2).join('   &') + '...'
            : row.work_flow_runner.split(',').join('   &')}
        </div>
      ),
    },
    {
      id: 'view',
      header: 'View',
      sortable: true,
      sortField: 'isChild',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.isChild}
        </div>
      ),
    },
    {
      id: 'started',
      header: 'STARTED',
      sortable: true,
      sortField: 'start_date',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.start_date}
        </div>
      ),
    },
    {
      id: 'due',
      header: 'DUE',
      sortable: true,
      sortField: 'end_date',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.end_date}
        </div>
      ),
    },
    {
      id: 'completed',
      header: '#Tasks Completed',
      sortable: false,
      sortField: 'completed_tasks',
      cell: (row: CourtNoticeItem) => (
        <div className="text-[14px] font-normal">{row.completed_tasks || ''}</div>
      ),
    },
    {
      id: 'status',
      header: 'STATUS',
      sortable: false,
      sortField: 'status',
      cell: (row: CourtNoticeItem) => (
        <div onClick={() => handleRowClick(row)} className="cursor-pointer">
          <StatusBadge status={mapStatusVariant(row.status)} variant="fill" />
        </div>
      ),
    },
    {
      id: 'assignee',
      header: 'ASSIGNEE',
      sortable: false,
      sortField: 'assigned_users',
      cell: (row: CourtNoticeItem) => {
        const assignedUsers = row.assigned_users
          ? row.assigned_users
              .split(',')
              .map(s => s.trim())
              .filter(Boolean)
          : [];
        const userCount = assignedUsers?.length || 0;
        const firstUser = assignedUsers[0];
        const roleKeywords = ['Paralegal', 'Attorney', 'Intake', 'Billing'];
        const hasRoleKeyword = assignedUsers.some(user =>
          roleKeywords.some(keyword => user.toLowerCase().includes(keyword.toLowerCase()))
        );

        return (
          <div
            className="relative w-[32px] h-[32px] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {hasRoleKeyword ? (
              <>
                <div className="w-[32px] h-[32px] rounded-full bg-[#3f73f65e] flex items-center justify-center">
                  <Image src="/assets/Group-icon.svg" alt="Group icon" width={25} height={25} />
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            ) : firstUser ? (
              <>
                <div className="w-[32px] h-[32px] rounded-full bg-[#5F6F84] text-white flex items-center justify-center font-semibold">
                  {getInitials(firstUser)}
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            ) : (
              <>
                <div className="w-[32px] h-[32px] rounded-full bg-[#C5D5FC] flex items-center justify-center font-semibold">
                  <Image
                    src="/assets/ai-robot-new-2.svg"
                    alt="AI assistant"
                    width={25}
                    height={25}
                  />
                </div>
                <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
                  {userCount}
                </span>
              </>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div className="p-6">
      <PageHeader
        title={viewTitle}
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onFilter={handleFilter}
        onColumns={handleColumns}
        onSaveView={handleSaveView}
        showSaveView={true} // Always show save button
        disableSaveView={!!savedView} // Disable save button if this is a saved view
      />

      {error && <div className="mt-4 p-3 bg-red-100 text-red-700 rounded">{error}</div>}

      <DataTable
        columns={columns}
        data={workflows}
        selectedIds={selectedWorkflows}
        onSelectRow={handleSelectWorkflow}
        onSelectAll={handleSelectAll}
        isAllSelected={selectedWorkflows.length === workflows.length && workflows.length > 0}
        className="mt-4"
        showHeader={true}
        isLoading={loading}
        currentSortBy={sortBy}
        currentSortOrder={sortOrder}
        onSort={handleSort}
        idField="id"
      />

      {!loading && workflows.length > 0 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={totalPages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
          className="mt-6"
        />
      )}

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </div>
  );
};

export default CourtNoticeArchived;
