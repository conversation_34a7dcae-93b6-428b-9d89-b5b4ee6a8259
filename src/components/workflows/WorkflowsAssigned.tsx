import React, { useState, useEffect } from 'react';
import { workflowService } from '@/services/api';
import courtNoticeService from '@/services/api/courtNoticeService';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { formatDate } from '@/utils/dateUtils';
import { DataTable, PageHeader, StatusBadge, Column } from '@/components/common';
import { Workflow } from '@/types/workflow';
import { CourtNoticeItem } from '@/types/courtNotice';
import Image from 'next/image';
import { mapStatusVariant } from '@/types/courtNotice';
import { useRouter } from 'next/router';
import SaveViewModal from '@/components/common/SaveViewModal';
import { UserViewService, UserView } from '@/services/userViewService';
import { toast } from 'react-hot-toast';

interface WorkflowsAssignedProps {
  _onNameClick: (id: string) => void;
  savedView?: UserView;
  viewTitle?: string;
}

const WorkflowsAssigned: React.FC<WorkflowsAssignedProps> = ({
  _onNameClick,
  savedView,
  viewTitle,
}) => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [workflows, setWorkflows] = useState<(Workflow | CourtNoticeItem)[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedWorkflows, setSelectedWorkflows] = useState<string[]>([]);
  const [showSaveViewModal, setShowSaveViewModal] = useState(false);
  const [savingView, setSavingView] = useState(false);
  const [currentVisibleColumns, setCurrentVisibleColumns] = useState<string[]>([]);
  const [hasColumnsChanged, setHasColumnsChanged] = useState(false);
  const [originalColumns, setOriginalColumns] = useState<string[]>([]);
  const [currentFilters, setCurrentFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);
  const [hasFiltersChanged, setHasFiltersChanged] = useState(false);
  const [originalFilters, setOriginalFilters] = useState<
    Array<{
      fieldName: string;
      filter: string;
      value: string[];
    }>
  >([]);
  const limit = 10;

  const fetchWorkflows = async (pageNum: number) => {
    try {
      setLoading(true);

      // Use court-notice-list API for saved views, fallback to original API for others
      console.log('Saved view detected:', savedView);
      if (savedView) {
        // Determine the type from saved view or default to my_workflow
        const workflowType = savedView?.type || 'my_work_flow';

        // Map the type to court notice API types
        const typeMapping: Record<string, string> = {
          my_work_flow: 'new-court-notice', // Use new-court-notice for my_workflow
          new_court_notice: 'new-court-notice',
          completed: 'completed',
          follow_up: 'follow-up',
          archive: 'archived',
        };

        const apiType = typeMapping[workflowType] || 'new-court-notice';

        const payload = {
          page: pageNum,
          limit: limit,
          type: apiType,
          view: 'View_1', // Always use View_1 for now to ensure compatibility
          filter: currentFilters || [], // Pass current filters
        };

        console.log('Applied filters in payload:', currentFilters);

        console.log('Court Notice API Payload:', payload);
        const response = await courtNoticeService.getCourtNoticeList(payload);
        console.log('Court Notice API Response received');

        // Try both possible locations for the data
        const newWorkflows = (response as any).data?.data?.fields || response.data?.fields || [];
        console.log('Extracted workflows count:', newWorkflows.length);

        // If it's the first page, replace the workflows
        // Otherwise append the new workflows to the existing ones
        if (pageNum === 1) {
          setWorkflows(newWorkflows);
        } else {
          setWorkflows(prev => [...(prev || []), ...newWorkflows]);
        }

        setHasMore(newWorkflows.length === limit);
      } else {
        // Fallback to original my-workflow API for non-saved views
        const response = await workflowService.getMyWorkflows(pageNum, limit);
        const newWorkflows = response.data?.workflows || [];

        // If it's the first page, replace the workflows
        // Otherwise append the new workflows to the existing ones
        if (pageNum === 1) {
          setWorkflows(newWorkflows);
        } else {
          setWorkflows(prev => [...(prev || []), ...newWorkflows]);
        }

        setHasMore(newWorkflows.length === limit);
      }
    } catch (error) {
      console.error('Error fetching workflows:', error);
    } finally {
      setLoading(false);
    }
  };

  // Reset and fetch initial data when component mounts or saved view changes
  useEffect(() => {
    setPage(1);
    setWorkflows([]);

    // Clear search term initially for saved views to show all results
    setSearchTerm('');

    // If there's a saved view, initialize search term from saved filters
    // if (savedView?.filters?.search && typeof savedView.filters.search === 'string') {
    //   setSearchTerm(savedView.filters.search);
    // }

    // Initialize visible columns based on saved view
    if (savedView?.columns && Array.isArray(savedView.columns)) {
      const savedColumnNames = savedView.columns
        .filter(col => col.visible !== false)
        .map(col => col.FieldName);

      // Normalize saved column names to match TableActions expected format
      const normalizeFieldName = (name: string) => {
        const normalized = name.toLowerCase().trim();
        // Handle common variations and map to standard names
        if (normalized === 'started') return 'Started';
        if (normalized === 'due on') return 'Due On';
        if (normalized === 'task completed') return 'Task Completed';
        if (normalized === 'status') return 'Status';
        if (normalized === 'activity') return 'Activity';
        if (normalized === 'assignee') return 'Assignee';
        if (normalized === 'name') return 'Name';
        // Return as-is if no mapping found
        return name;
      };

      const normalizedColumnNames = savedColumnNames.map(normalizeFieldName);
      setCurrentVisibleColumns(normalizedColumnNames);
      setOriginalColumns(normalizedColumnNames); // Store original for comparison
    } else {
      // Default visible columns if no saved view
      setCurrentVisibleColumns([
        'Name',
        'Started',
        'Due On',
        'Task Completed',
        'Status',
        'Activity',
        'Assignee',
      ]);
    }

    // Initialize filters based on saved view
    if (savedView?.filter && Array.isArray(savedView.filter)) {
      const savedFilters = savedView.filter.map(filter => ({
        fieldName: filter.fieldName,
        filter: filter.filter,
        value: filter.value,
      }));
      setCurrentFilters(savedFilters);
      setOriginalFilters(savedFilters); // Store original for comparison
    } else {
      // Reset filters if no saved view
      setCurrentFilters([]);
      setOriginalFilters([]);
    }

    fetchWorkflows(1);
  }, [savedView]);

  // Fetch more data when page changes or filters change
  useEffect(() => {
    if (page > 1) {
      fetchWorkflows(page);
    }
  }, [page]);

  // Re-fetch data when filters change
  useEffect(() => {
    console.log('Filters changed, re-fetching data:', currentFilters);
    setPage(1); // Reset to page 1 when filters change
    setWorkflows([]); // Clear existing workflows
    fetchWorkflows(1);
  }, [currentFilters]);

  // Filter workflows based on search term
  const filteredWorkflows = (workflows || []).filter(workflow => {
    // If no search term, show all workflows
    if (!searchTerm || searchTerm.trim() === '') {
      return true;
    }

    // Handle CourtNoticeItem structure
    if ('template_name' in workflow) {
      const matches =
        workflow.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.matter.toLowerCase().includes(searchTerm.toLowerCase());
      return matches;
    }
    // Handle Workflow structure
    else if ('work_flow_name' in workflow) {
      const matches =
        workflow.work_flow_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        workflow.view.some((v: string) => v.toLowerCase().includes(searchTerm.toLowerCase()));
      return matches;
    }
    return false;
  });

  // Debug: Uncomment these lines if you need to debug filtering issues
  // console.log('Workflows state:', workflows);
  // console.log('Filtered workflows:', filteredWorkflows);
  // console.log('Search term:', searchTerm);

  const lastWorkflowElementRef = useInfiniteScroll({
    loading,
    hasMore,
    onLoadMore: () => setPage(prev => prev + 1),
  });

  const handleSelectWorkflow = (id: string, selected: boolean) => {
    if (selected) {
      setSelectedWorkflows(prev => [...prev, id]);
    } else {
      setSelectedWorkflows(prev => prev.filter(wfId => wfId !== id));
    }
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected && filteredWorkflows.length > 0) {
      setSelectedWorkflows(
        filteredWorkflows
          .map(workflow => {
            // Handle different id fields for different data types
            if ('_id' in workflow) {
              return workflow._id;
            } else if ('id' in workflow) {
              return workflow.id;
            }
            return '';
          })
          .filter(id => id !== '')
      );
    } else {
      setSelectedWorkflows([]);
    }
  };

  const handleFilter = () => {
    console.log('Filter button clicked');
  };

  const handleColumns = (selectedColumns?: string[]) => {
    console.log('Columns button clicked', selectedColumns);
    if (selectedColumns) {
      // Use standard logic: selectedColumns = visible columns
      setCurrentVisibleColumns(selectedColumns);

      // Check if columns have changed from original (only for saved views)
      if (savedView && originalColumns.length > 0) {
        const columnsChanged =
          JSON.stringify([...selectedColumns].sort()) !==
          JSON.stringify([...originalColumns].sort());
        setHasColumnsChanged(columnsChanged);
      }
    }
  };

  const handleFilters = (
    filters: Array<{
      id: string;
      field: string;
      operator: string;
      value: string;
      selectedValues?: string[];
    }>
  ) => {
    console.log('Filters changed', filters);

    // Convert FilterRow format to backend format
    const backendFilters = filters
      .filter(f => f.field && f.operator && (f.value || f.selectedValues?.length))
      .map(f => {
        // Map frontend field names to backend field names
        const fieldMap: Record<string, string> = {
          Name: 'template_name',
          Assignee: 'assigned_users',
          Status: 'status',
          Contact: 'contact',
          Matter: 'matter',
          Templates: 'templates',
          'Due date': 'due_date',
          'Create date': 'created_date',
          Attorney: 'attorney',
        };

        // Map frontend operators to backend operators
        const operatorMap: Record<string, string> = {
          contains: 'contains',
          equals: 'equal',
          not_contains: 'not_contains',
          not_equals: 'not_equal',
          before: 'before',
          after: 'after',
          between: 'between',
        };

        const fieldName = fieldMap[f.field] || f.field;
        const operator = operatorMap[f.operator] || f.operator;
        const value =
          f.selectedValues && f.selectedValues.length > 0
            ? f.selectedValues
            : f.value.split(', ').filter(v => v.trim());

        return {
          fieldName,
          filter: operator,
          value,
        };
      });

    setCurrentFilters(backendFilters);

    // Check if filters have changed from original (only for saved views)
    if (savedView && originalFilters.length > 0) {
      const filtersChanged =
        JSON.stringify(backendFilters.sort((a, b) => a.fieldName.localeCompare(b.fieldName))) !==
        JSON.stringify(originalFilters.sort((a, b) => a.fieldName.localeCompare(b.fieldName)));
      setHasFiltersChanged(filtersChanged);
    } else if (backendFilters.length > 0) {
      // If we have filters but no saved view, mark as changed
      setHasFiltersChanged(true);
    } else {
      setHasFiltersChanged(false);
    }
  };

  const handleSaveView = async () => {
    // If we're in a saved view and either columns or filters have changed, update directly without modal
    if (savedView && (hasColumnsChanged || hasFiltersChanged)) {
      await handleUpdateExistingView();
    } else {
      // Normal flow - open modal for new view
      setShowSaveViewModal(true);
    }
  };

  const handleUpdateExistingView = async () => {
    try {
      setSavingView(true);

      // Prepare updated view data
      const updatedViewData = {
        viewId: savedView?.id || '',
        name: savedView?.name,
        type: savedView?.type || 'my_work_flow',
        userId: savedView?.userId || 1,
        filters: {
          search: searchTerm,
          sortBy: '',
          sortOrder: 'asc',
        },
        columns: currentVisibleColumns.map(col => ({
          FieldName: col,
          searchable: true,
          sortable: true,
          visible: true,
        })),
        filter: currentFilters,
      };

      const response = await UserViewService.updateUserView(updatedViewData);

      if (response.data.success) {
        console.log('View updated successfully:', response.data.view);

        // Show success toast notification
        toast.success('View updated successfully');

        // Reset the changed flags
        setHasColumnsChanged(false);
        setHasFiltersChanged(false);
        // Update original state to new state
        setOriginalColumns([...currentVisibleColumns]);
        setOriginalFilters([...currentFilters]);

        // Refresh sidebar views if function exists
        const refreshSidebarViews = (window as unknown as { refreshSidebarViews?: () => void })
          .refreshSidebarViews;
        if (refreshSidebarViews) {
          refreshSidebarViews();
        }
      } else {
        console.error('Failed to update view:', response.data.message);
        // Show error toast
        toast.error('Failed to update view');
      }
    } catch (error) {
      console.error('Error updating view:', error);
      // Show error toast for network/API errors
      toast.error('Error updating view. Please try again.');
    } finally {
      setSavingView(false);
    }
  };

  const handleSaveViewSubmit = async (viewName: string) => {
    try {
      setSavingView(true);

      // Get current filters and columns state
      const viewData = {
        name: viewName,
        type: 'my_work_flow', // enum: [my_work_flow, completed, archive, new_court_notice, follow_up]
        userId: 1, // TODO: Get from auth context
        filters: {
          search: searchTerm,
          sortBy: '',
          sortOrder: 'asc',
        },
        columns: currentVisibleColumns.map(col => ({
          FieldName: col,
          searchable: true,
          sortable: true,
          visible: true,
        })),
        filter: currentFilters,
      };

      const response = await UserViewService.saveUserView(viewData);

      if (response.data.success) {
        toast.success('View saved successfully');
        setShowSaveViewModal(false);
      } else {
        toast.error('Failed to save view');
      }
    } catch (error) {
      console.error('Error saving view:', error);
      toast.error('Failed to save view');
    } finally {
      setSavingView(false);
    }
  };

  const handleRowClick = (rowData: Workflow | CourtNoticeItem) => {
    // Handle CourtNoticeItem structure
    if ('template_name' in rowData) {
      router.push(`/workflowrun?taskId=${rowData.last_task_id}&work_flow_id=${rowData._id}`);
    }
    // Handle Workflow structure
    else if ('work_flow_name' in rowData) {
      router.push(`/workflowrun?taskId=${rowData.latest_task_id}&work_flow_id=${rowData.id}`);
    }
  };

  // Define all available columns
  const allColumns: Column[] = [
    {
      id: 'name',
      header: 'Name',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getName = () => {
          if ('template_name' in row) {
            return row.template_name.split(',').length > 2
              ? row.template_name.split(',').slice(0, 2).join('   &') + '...'
              : row.template_name.split(',').join('   &');
          } else if ('work_flow_name' in row) {
            return row.work_flow_name.split(',').length > 2
              ? row.work_flow_name.split(',').slice(0, 2).join('   &') + '...'
              : row.work_flow_name.split(',').join('   &');
          }
          return '';
        };

        return (
          <div
            className="text-[14px] font-medium cursor-pointer text-[#2A2E34]"
            onClick={() => handleRowClick(row)}
          >
            <span>{getName()}</span>
          </div>
        );
      },
    },
    {
      id: 'started',
      header: 'Started',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div
          className="text-[14px] text-[#5F6F84] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {formatDate(row.start_date)}
        </div>
      ),
    },
    {
      id: 'dueOn',
      header: 'Due On',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div
          className="text-[14px] text-[#5F6F84] cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          {formatDate(row.end_date)}
        </div>
      ),
    },
    {
      id: 'taskCompleted',
      header: 'Task Completed',
      cell: (row: Workflow | CourtNoticeItem) => {
        const getTaskCompleted = () => {
          if ('completed_tasks' in row) {
            return row.completed_tasks || '';
          } else if ('task_complete' in row) {
            return row.task_complete;
          }
          return '';
        };

        return (
          <div
            className="text-[14px] text-[#5F6F84] cursor-pointer"
            onClick={() => handleRowClick(row)}
          >
            {getTaskCompleted()}
          </div>
        );
      },
    },
    {
      id: 'status',
      header: 'Status',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div onClick={() => handleRowClick(row)} className="cursor-pointer">
          <StatusBadge status={mapStatusVariant(row.status)} />
        </div>
      ),
    },
    {
      id: 'activity',
      header: 'Activity',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div className="text-[14px] text-[#5F6F84]">{row.last_activity}</div>
      ),
    },
    {
      id: 'assignee',
      header: 'Assignee',
      cell: (row: Workflow | CourtNoticeItem) => (
        <div
          className="relative bg-[#3f73f65e] w-7 h-7 rounded-full flex items-center justify-center cursor-pointer"
          onClick={() => handleRowClick(row)}
        >
          <Image src="/assets/ai-robot-new-2.svg" alt="AI assistant" width={25} height={25} />
          <span className="absolute -bottom-1 -right-1 bg-[#3F73F6] text-white text-[10px] w-4 h-4 flex items-center justify-center rounded-full">
            3
          </span>
        </div>
      ),
    },
  ];

  // Filter columns based on current visible columns configuration
  const { columns, visibleColumnNames } = React.useMemo(() => {
    if (currentVisibleColumns.length > 0) {
      // Get the field names from current visible columns
      const visibleColumnNames = currentVisibleColumns.map(col => col.toLowerCase());

      // Create a mapping for field name normalization
      const normalizeFieldName = (name: string) => {
        const normalized = name.toLowerCase().trim();
        // Handle common variations
        if (normalized === 'started') return 'started';
        if (normalized === 'due on') return 'dueon';
        if (normalized === 'task completed') return 'taskcompleted';
        if (normalized === 'status') return 'status';
        if (normalized === 'activity') return 'activity';
        if (normalized === 'assignee') return 'assignee';
        if (normalized === 'name') return 'name';
        return normalized.replace(/\s+/g, '');
      };

      // Filter columns to only show those in current visible columns
      const filteredColumns = allColumns.filter(col => {
        const normalizedColumnId = normalizeFieldName(col.id);
        const normalizedColumnHeader = normalizeFieldName(col.header);

        return visibleColumnNames.some(visibleName => {
          const normalizedVisibleName = normalizeFieldName(visibleName);
          return (
            normalizedVisibleName === normalizedColumnId ||
            normalizedVisibleName === normalizedColumnHeader
          );
        });
      });

      // Get the actual column headers for the visible columns
      const actualVisibleNames = filteredColumns.map(col => col.header);

      return {
        columns: filteredColumns,
        visibleColumnNames: actualVisibleNames,
      };
    }

    // If no current visible columns set, return all columns
    const allVisibleNames = allColumns.map(col => col.header);
    return {
      columns: allColumns,
      visibleColumnNames: allVisibleNames,
    };
  }, [currentVisibleColumns, allColumns]);

  return (
    <div className="p-8 h-full">
      <PageHeader
        title={viewTitle || 'Workflows Assigned to Me'}
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        onFilter={handleFilter}
        onColumns={handleColumns}
        onSaveView={handleSaveView}
        showSaveView={true} // Always show save button
        disableSaveView={savedView ? !(hasColumnsChanged || hasFiltersChanged) : false} // Enable if columns or filters changed in saved view
        visibleColumns={visibleColumnNames} // Pass visible column names
        savedFilters={currentFilters} // Pass saved filters
        onFiltersChange={handleFilters} // Pass filter change handler
      />

      <DataTable
        columns={columns}
        data={filteredWorkflows}
        selectedIds={selectedWorkflows}
        onSelectRow={handleSelectWorkflow}
        onSelectAll={handleSelectAll}
        isAllSelected={
          selectedWorkflows.length === filteredWorkflows.length && filteredWorkflows.length > 0
        }
        isLoading={loading}
        lastItemRef={lastWorkflowElementRef}
        className="mt-8"
      />

      <SaveViewModal
        isOpen={showSaveViewModal}
        onClose={() => setShowSaveViewModal(false)}
        onSave={handleSaveViewSubmit}
        isLoading={savingView}
      />
    </div>
  );
};

export default WorkflowsAssigned;
