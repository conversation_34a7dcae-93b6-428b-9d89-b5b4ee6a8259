/**
 * Event Type Definitions
 * These types define the structure of event data in the application
 */

// Event status enum for type safety
export type EventStatus = 'Active' | 'Completed' | 'Cancelled' | 'Pending';

// Event type enum
export type EventType = 'Court Notice' | 'Discovery' | 'Client meeting' | 'Court Appearance' | 'Deposition' | 'Conference';

// Court notice type enum
export type CourtNoticeType = 'Jury Trial' | 'Motion Hearing' | 'Status Conference' | 'Settlement Conference' | 'Trial';

// Main Event interface
export interface Event {
  id: string;
  eventType: EventType;
  courtNoticeType?: CourtNoticeType;
  attorney: string;
  start: string; // Can be date-time or duration like "1.5 hrs"
  end: string; // Can be date-time or end date
  location: string;
  status: EventStatus;
  contactId: string; // Reference to contact/client
  matterId?: string; // Optional reference to matter
  description?: string;
  notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Event list query parameters
export interface EventListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: EventStatus;
  eventType?: EventType;
  attorney?: string;
  contactId?: string;
  matterId?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: keyof Event;
  sortOrder?: 'asc' | 'desc';
}

// Event API response interface
export interface EventListResponse {
  data: Event[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Event form data interface
export interface EventFormData {
  eventType: EventType;
  courtNoticeType?: CourtNoticeType;
  attorney: string;
  start: string;
  end: string;
  location: string;
  status: EventStatus;
  contactId: string;
  matterId?: string;
  description?: string;
  notes?: string;
}

// Event filter interface
export interface EventFilters {
  status: EventStatus[];
  eventType: EventType[];
  attorney: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}

// Event actions interface for bulk operations
export interface EventAction {
  type: 'delete' | 'update_status' | 'reschedule' | 'export';
  eventIds: string[];
  data?: {
    status?: EventStatus;
    newDate?: string;
  };
} 