/**
 * Court Notice related types
 */

// Court Notice item structure

export interface TableViewColumn {
  fieldName: string;
  isSorting: boolean;
  isFilter: boolean;
  isShowing: boolean;
}
export interface CourtNoticeItem {
  id: number;
  template_name: string;
  start_date: string;
  end_date: string;
  last_activity: string;
  status: string;
  assigned_users: string;
  assign_by: string;
  run_by: string;
  notes: string;
  work_flow_runner: string;
  attorney: string;
  matter: string;
  last_task_id: string;
  _id: string;
  isChild?: string;
  completed_tasks?: string;
}

// API response structure for Court Notice list
export interface CourtNoticeListResponse {
  data: {
    fields: CourtNoticeItem[];
    total: number;
    page: number;
    limit: number;
    column: TableViewColumn[];
  };
  statusCode: number;
  message: string;
}

// Filter structure for POST API
export interface CourtNoticeFilter {
  fieldName: string;
  filter: string;
  value?: string[];
  dateRange?: {
    from: string;
    to: string;
  };
}

// POST payload parameters for Court Notice list API
export interface CourtNoticeListParams {
  page: number;
  limit: number;
  type: string;
  userId?: string; // Made optional since it's automatically added by the service
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  sort_by?: string; // API format
  sort_order?: 'asc' | 'desc'; // API format
  view?: string; // Added for table view configuration
  filter?: CourtNoticeFilter[]; // Added for filtering support
}

// Status state for Court Notice module
export interface CourtNoticeStatus {
  loading: boolean;
  error: string | null;
}

// Status variant mapping - API to Display format
export const mapStatusVariant = (
  status: string
): 'On Track' | 'Pending' | 'Completed' | 'Overdue' | 'Due Soon' | 'Archived' => {
  const statusMap: Record<
    string,
    'On Track' | 'Pending' | 'Completed' | 'Overdue' | 'Due Soon' | 'Archived'
  > = {
    PENDING: 'Pending',
    COMPLETED: 'Completed',
    IN_PROGRESS: 'On Track',
    ON_TRACK: 'On Track',
    OVERDUE: 'Overdue',
    DUE_SOON: 'Due Soon',
    ARCHIVED: 'Archived',
  };

  return statusMap[status] || 'Pending';
};

// Convert display status to API format (spaces to underscores, uppercase)
export const convertStatusToApiFormat = (displayStatus: string): string => {
  const displayToApiMap: Record<string, string> = {
    'On Track': 'ON_TRACK',
    'Pending': 'PENDING',
    'Completed': 'COMPLETED',
    'Overdue': 'OVERDUE',
    'Due Soon': 'DUE_SOON',
    'Archived': 'ARCHIVED',
  };

  return displayToApiMap[displayStatus] || displayStatus.toUpperCase().replace(/\s+/g, '_');
};

// Convert array of display status values to API format
export const convertStatusArrayToApiFormat = (statusArray: string[]): string[] => {
  return statusArray.map(status => convertStatusToApiFormat(status));
};

/**
 * Check if a string value contains "TBD" (case insensitive)
 * @param value The string to check
 * @returns True if the string contains "TBD", false otherwise
 */
export const isTbdValue = (value: string | null | undefined): boolean => {
  return typeof value === 'string' && value.toLowerCase().includes('tbd');
};

/**
 * Fields that should be checked for TBD values in court notice events
 */
export const tbdCheckFields = [
  'courtNoticeType',
  'appointmentAction',
  'charge',
  'county',
  'courtLocation',
  'clientAttendance',
  'meetingLocation',
];

/**
 * Count the number of TBD fields in a court notice event
 * @param event The court notice event to check
 * @returns The number of fields with TBD values
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const countTbdFields = (event: Record<string, any>): number => {
  let count = 0;

  // Check standard fields
  tbdCheckFields.forEach(field => {
    if (isTbdValue(event[field])) {
      count++;
    }
  });

  // Also check meeting details based on the meeting location type
  if (event.meetingLocation) {
    if (event.meetingLocation.includes('Virtual meeting link') && isTbdValue(event.meetingLink)) {
      count++;
    }

    if (event.meetingLocation.includes('Phone') && isTbdValue(event.phoneDetails)) {
      count++;
    }

    if (event.meetingLocation.includes('Meeting in person') && isTbdValue(event.meetingAddress)) {
      count++;
    }
  }

  return count;
};
