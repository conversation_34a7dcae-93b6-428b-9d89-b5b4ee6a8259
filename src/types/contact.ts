/**
 * Contact Type Definitions
 * These types define the structure of contact data in the application
 */

// Contact status enum for type safety
export type ContactStatus = 'Active' | 'Closed';

// Contact type enum
export type ContactType = 'Client' | 'Lead' | 'Prospect' | 'Former Client';

// Main Contact interface
export interface Contact {
  id: string;
  name: string;
  status: ContactStatus;
  type: ContactType;
  email: string;
  mobile: string;
  attorney: string;
  createdAt?: string;
  updatedAt?: string;
  notes?: string;
  company?: string;
  address?: ContactAddress;
  alternateContacts?: AlternateContact[];
  syncStatus?: string;
}

// Contact address interface
export interface ContactAddress {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}

// Alternate contact interface for additional contact methods
export interface AlternateContact {
  id: string;
  type: 'phone' | 'email' | 'fax';
  value: string;
  label?: string;
  isPrimary?: boolean;
}

// Contact list query parameters
export interface ContactListParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ContactStatus;
  type?: ContactType;
  sortBy?: keyof Contact;
  sortOrder?: 'asc' | 'desc';
  attorney?: string;
}

// Contact API response interface
export interface ContactListResponse {
  data: Contact[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Contact form data interface
export interface ContactFormData {
  name: string;
  email: string;
  mobile: string;
  type: ContactType;
  status: ContactStatus;
  attorney?: string;
  notes?: string;
  company?: string;
  address?: ContactAddress;
}

// Contact filter interface
export interface ContactFilters {
  status: ContactStatus[];
  type: ContactType[];
  attorney: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}

// Contact actions interface for bulk operations
export interface ContactAction {
  type: 'delete' | 'update_status' | 'assign_attorney' | 'export';
  contactIds: string[];
  data?: {
    status?: ContactStatus;
    attorney?: string;
  };
} 