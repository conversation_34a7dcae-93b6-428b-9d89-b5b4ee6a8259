/**
 * Workflow Type Definitions
 * These types define the structure of workflow data in the application
 */

// Basic form field types
export interface FormField {
  id: string;
  type: string;
  placeholder?: string;
  label?: string;
  value?: string | boolean | number;
  options?: Array<{ value: string; text: string }>;
  form_field_id?: string;
}

// Section of form fields
export interface FormSection {
  id: string;
  type: string;
  title: string;
  fields: FormField[];
}

// Task within a workflow
export interface Task {
  id: string;
  name: string;
  description: string;
  icon: string;
  status: 'active' | 'pending' | 'completed' | 'skipped' | '';
  formFields: FormSection[];
}

// Workflow containing tasks
export interface Workflow {
  id: string;
  work_flow_name: string;
  view: string[];
  start_date: string;
  end_date: string;
  task_complete: string;
  status: string;
  last_activity: string;
  assign: { name: string }[];
  latest_task_id: string;
  notes: string;
}

// Contact Workflow interface for displaying workflows in contact detail page
export interface ContactWorkflow {
  id: string;
  workflowRun: string;
  template: string;
  status: 'Open' | 'Closed' | 'On Track' | 'Delayed' | 'Completed';
  openDate: string;
  closedDate?: string;
  dueDate: string;
  contactId: string;
  assignee?: string;
  description?: string;
  notes?: string;
}

// Workflow status type
export type WorkflowStatus = 'Open' | 'Closed' | 'On Track' | 'Delayed' | 'Completed';

// Response structure from the workflow API
export interface WorkflowApiResponse {
  data: {
    workflows: Workflow[];
    client_not_found?: boolean;
    advisor_message?: string;
    advisor_color?: string;
    complete_button_disabled?: boolean;
  };
  statusCode: number;
  message: string;
}

// Props for workflow components
export interface WorkflowRunProps {
  initialTaskId?: string;
}

// Form data structure
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type FormDataType = Record<string, any>;

// Task action types
export type TaskAction = 'skip' | 'complete' | 'save';

// Status of workflow API calls
export interface ApiStatus {
  loading: boolean;
  error: string | null;
  success: boolean;
}

export interface Assignee {
  id: string;
  name: string;
}

export interface WorkflowResponse {
  data: {
    workflows: Workflow[];
    total: number;
    page: number;
    limit: number;
  };
  statusCode: number;
  message: string;
}

// Contact Workflow list response
export interface ContactWorkflowResponse {
  data: ContactWorkflow[];
  total: number;
  page: number;
  limit: number;
}
