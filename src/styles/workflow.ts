/**
 * Workflow Styles
 * Centralized styles for workflow components
 */

// Disabled state colors
export const disabledColors = {
  text: '#C7D1DF',
  border: '#DCE2EB',
  background: '#F3F5F9',
  icon: '#C7D1DF',
};

// Disabled state styles with proper Tailwind classes
export const disabledStyles = {
  base: 'cursor-not-allowed bg-[#F3F5F9] border-[#DCE2EB] text-[#A2AFC2]',
  input: 'cursor-not-allowed bg-[#F3F5F9] border-[#DCE2EB] text-[#A2AFC2]',
  button: 'cursor-not-allowed border-[#DCE2EB] text-[#A2AFC2]',
  icon: 'text-[#C7D1DF]',
  dropdown: 'cursor-not-allowed bg-[#F3F5F9] text-[#A2AFC2]',
  banner: 'cursor-not-allowed border-[#DCE2EB] text-[#C7D1DF]',
};

// Utility function to get disabled classes
export const getDisabledClasses = (type: 'input' | 'button' | 'icon' | 'dropdown' | 'banner' = 'input') => {
  return disabledStyles[type];
};

// Utility function to conditionally apply disabled styling
export const conditionalDisabled = (isDisabled: boolean, type: 'input' | 'button' | 'icon' | 'dropdown' | 'banner' = 'input', enabledClasses = '') => {
  return isDisabled ? getDisabledClasses(type) : enabledClasses;
};

export const workflowStyles = {
  // Container styles
  container: 'space-y-6',

  // Header section
  headerSection: 'flex items-center justify-between mb-6',
  assignToSection: 'flex items-center text-[#2A2E34] text-[14px]',
  assignIcon: 'w-4 h-4 text-[#2A2E34] mr-2',
  dueDateSection: 'flex items-center text-[#2A2E34] text-[14px]',
  dueDateIcon: 'w-4 h-4 text-[#2A2E34] mr-2',

  // Field containers
  fieldContainer: 'relative mb-4',

  // Title styles
  fieldTitle:
    'bg-[#3F73F6] text-white py-3 px-5 w-fit rounded-[12px] text-base rounded-bl-none font-medium text-[14px] leading-[20px]',
  titleIcon: 'absolute left-3 top-3 text-white',

  // Input containers
  inputsContainer: 'mt-2',

  // Generic input styles - can be used for any input type
  textInput:
    'w-full p-2 rounded-[12px] h-[40px] rounded-[12px] border border-[#DCE2EB] py-[14px] px-[16px] text-[#2A2E34] ring-inset placeholder:text-[14px] placeholder:text-[#5F6F84] placeholder:font-normal focus:ring focus:ring-inset focus:border-[1px] focus:border-[#3f73f650] focus:ring-[#3F73F6] focus:outline-none',
  emailInput:
    'w-full border border-[#DCE2EB] p-2 rounded-[12px] h-[40px] rounded-[12px] border border-[#DCE2EB] py-[14px] px-[16px] text-[#2A2E34] ring-inset placeholder:text-[14px] placeholder:text-[#5F6F84] placeholder:font-normal focus:ring focus:ring-inset focus:border-[1px] focus:border-[#3f73f650] focus:ring-[#3F73F6] focus:outline-none',
  phoneInput:
    'w-full border border-[#DCE2EB] p-2 rounded-[12px] h-[40px] rounded-[12px] border border-[#DCE2EB] py-[14px] px-[16px] text-[#2A2E34] ring-inset placeholder:text-[14px] placeholder:text-[#5F6F84] placeholder:font-normal focus:ring focus:ring-inset focus:border-[1px] focus:border-[#3f73f650] focus:ring-[#3F73F6] focus:outline-none',
  textareaInput:
    'w-full border border-[#DCE2EB] p-2 rounded-[12px] h-24 rounded-[12px] border border-[#DCE2EB] py-[14px] px-[16px] text-[#2A2E34] ring-inset placeholder:text-[14px] placeholder:text-[#5F6F84] placeholder:font-normal focus:ring focus:ring-inset focus:border-[1px] focus:border-[#3f73f650] focus:ring-[#3F73F6] focus:outline-none',
  selectInput:
    'w-full border border-[#DCE2EB] rounded-[12px] h-[40px] appearance-none rounded-[12px] border border-[#DCE2EB] px-[16px] text-[#2A2E34] placeholder:text-[14px] placeholder:text-[#5F6F84] placeholder:font-normal',

  // Multi-part inputs (like name fields)
  multiPartContainer: 'flex space-x-2 mt-2',
  multiPartInput: 'flex-1 border border-[#DCE2EB] p-2 rounded-[12px]',

  // Checkbox styles
  checkboxContainer: 'flex items-center text-[#2A2E34] mt-2',
  checkbox: 'mr-2 h-4 w-4',

  // Select field specific styles
  selectContainer: 'relative',
  selectArrow: 'absolute right-2 top-2 text-[#2A2E34] pointer-events-none',
  selectArrowIcon: 'w-5 h-5 text-[#2A2E34]',

  // Text styles
  label: 'text-[14px] text-[#2A2E34]',
  header: 'text-[#2A2E34]',

  // Button styles
  buttonContainer: 'flex cursor-pointer space-x-2 mt-8',
  buttonSecondary:
    'flex-1 border border-[#3F73F6] cursor-pointer text-[#3F73F6] rounded-[12px] py-2 text-center',
  buttonPrimary: 'flex-1 bg-[#3F73F6] cursor-pointer text-white rounded-[12px] py-2 text-center',

  // Task list styles
  taskList: 'flex flex-col space-y-2 mt-4',
  taskItem: 'flex items-center p-2 w-[320px] border border-[#DCE2EB] rounded-[12px] cursor-pointer',
  taskItemActive: 'bg-blue-50',
  taskItemInactive: 'hover:bg-gray-100',
  taskItemCompleted: 'bg-green-50',
  taskItemSkipped: 'bg-gray-50',
  taskStatusDot: 'w-4 h-4 rounded-full mr-2',
  taskStatusActive: 'border-2 border-[#3F73F6]',
  taskStatusInactive: 'border-2 border-[#DCE2EB]',
  taskStatusCompleted: 'bg-green-500',
  taskStatusSkipped: 'bg-yellow-500',
  taskTextContainer: 'text-[14px] text-[#2A2E34] flex items-center',
  taskCompletedText: 'text-[#8cf1bd] text-[#2A2E34]',
  taskSkippedText: 'text-[#2A2E34] line-through',
  taskIcon: 'ml-1 w-5 h-5 rounded-full bg-gray-100 flex items-center justify-center',

  // Court Notice styles
  courtNoticeContainer: 'w-full',
  courtNoticeHeader: 'flex items-center bg-blue-100 rounded-lg p-3 mb-4',
  courtNoticeHeaderText: 'text-[#3F73F6] font-medium',
  courtNoticeCard: 'mb-4 border border-[#C7D1DF] rounded-[20px] relative overflow-visible',
  courtNoticeClientHeader: 'bg-white p-4 rounded-[20px] border-gray-200',
  courtNoticeEventsContainer: 'bg-white p-4 rounded-[20px]',
  courtNoticeEvent: 'border border-[#C7D1DF] rounded-[4px] p-[12px] mb-3',
  courtNoticeEventDetails: 'flex-1',
  courtNoticeEventClientName:
    'text-[14px] leading-[20px] font-normal text-gray-[#2A2E34] font-medium',
  courtNoticeEventClientName2:
    'text-[14px] leading-[25px] font-normal text-gray-[#2A2E34] font-medium',
  courtNoticeEventCaseNumber: 'text-[14px] leading-[20px] font-normal text-gray-[#2A2E34]',
  courtNoticeEventDescription: 'text-[14px] font-normal leading-[20px]',
  courtNoticeEventTiming: 'mt-2 text-[14px] leading-[20px] font-normal text-gray-500',
  courtNoticeActionButtons: 'flex',
  courtNoticeButtonCompleted: 'p-2 rounded-full bg-green-50 text-green-500 cursor-pointer',
  courtNoticeButtonPending: 'p-2 rounded-full text-gray-400 cursor-pointer',
  courtNoticeAddButton:
    'w-full flex items-center text-[#3F73F6] rounded-[12px] transition',

  // New field types
  instructionContainer: 'p-3 bg-gray-50 rounded-lg text-gray-700',
  radioButtonGroup: 'mt-2 flex space-x-2',
  radioButtonActive: 'px-4 py-2 rounded-lg bg-blue-500 text-white',
  radioButtonInactive: 'px-4 py-2 rounded-lg bg-white border border-[#DCE2EB] text-gray-700',
  actionButton:
    'w-full py-2 bg-blue-50 text-[#3F73F6] border border-blue-200 rounded-lg hover:bg-blue-100 transition',
  successMessage: 'p-3 bg-green-50 text-green-700 rounded-lg border border-green-200',
  confirmationBox:
    'p-3 bg-blue-50 text-[#3F73F6] rounded-lg border border-blue-200 flex items-center',
  assignSelect:
    'w-full bg-white border border-[#DCE2EB] rounded-[12px] py-2 pl-3 pr-10 text-[14px] leading-[20px] focus:outline-none focus:ring-2 focus:ring-blue-500',
};

// Export common field types that might be used in workflow forms
export const fieldTypes = {
  TEXT: 'text',
  EMAIL: 'email',
  PHONE: 'tel',
  TEXTAREA: 'text_area',
  CHECKBOX: 'checkbox',
  SELECT: 'select',
  RADIO: 'radio',
  DATE: 'date',
  TIME: 'time',
  NUMBER: 'number',
  FULLNAME: 'fullName',
  CONTACT_TYPE: 'contactType',
  NOTES: 'notes',
  CALL: 'call',
  CONFLICT: 'conflict',
  COURT_NOTICE: 'courtNotice',
  INSTRUCTION: 'instruction',
  RADIO_BUTTON_GROUP: 'radio_button_group',
  BUTTON: 'button',
  SUCCESS_MESSAGE: 'success_message',
  CONFIRMATION: 'confirmation',
  ASSIGN: 'assign',
  SEARCHABLE_SELECT: 'searchable_select',
};
