/* Column Resizer Styles */
.column-grip {
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  background: transparent;
  cursor: col-resize;
  z-index: 10;
}

.column-grip:hover {
  background: #F3F5F9;
  opacity: 0.7;
}

.column-dragging {
  cursor: col-resize !important;
  user-select: none;
}

.column-dragging * {
  cursor: col-resize !important;
  user-select: none;
}

/* Table header styles for resizable columns */
.resizable-table th {
  position: relative;
  border-right: 1px solid #e5e7eb;
}

.resizable-table th:last-child {
  border-right: none;
}

.resizable-table th:hover .column-grip {
  background: #f8f8fa;
  opacity: 0.5;
}

/* Ensure table layout is fixed for proper resizing */
.resizable-table {
  table-layout: fixed;
  width: 100%;
}

/* Prevent text selection during drag */
.column-dragging,
.column-dragging * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Visual feedback during resize */
.column-resizer-grip {
  position: absolute;
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  background: transparent;
  cursor: col-resize;
  z-index: 999;
}

.column-resizer-grip:hover,
.column-resizer-grip:active {
  background: #ffffff;
  opacity: 0.8;
}

/* Smooth transition for column width changes */
.resizable-table th,
.resizable-table td {
  transition: width 0.1s ease-out;
}

/* Disable transitions during active dragging */
.column-dragging .resizable-table th,
.column-dragging .resizable-table td {
  transition: none;
}
