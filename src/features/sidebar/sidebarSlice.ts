import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SidebarState } from '@/redux/types';

const initialState: SidebarState = {
  collapsed: false,
};

export const sidebarSlice = createSlice({
  name: 'sidebar',
  initialState,
  reducers: {
    toggleSidebar: state => {
      state.collapsed = !state.collapsed;
    },
    setSidebarState: (state, action: PayloadAction<boolean>) => {
      state.collapsed = action.payload;
    },
  },
});

export const { toggleSidebar, setSidebarState } = sidebarSlice.actions;
export default sidebarSlice.reducer;
