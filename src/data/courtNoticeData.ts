import { v4 as uuidv4 } from 'uuid';

// Sample data for court notices
export interface EventType {
  id: string;
  caseNumber: string;
  clientName: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  isCompleted?: boolean;
}

export interface ClientMatter {
  id: string;
  name: string;
  caseDescription?: string;
}

// Sample clients/matters
export const clients: ClientMatter[] = [
  {
    id: '1',
    name: '<PERSON><PERSON> | <PERSON><PERSON> (DWI 2nd-Lubbock) SA',
  },
  {
    id: '2',
    name: '<PERSON> | <PERSON> (DWI 2nd-Lubbock) SA',
  },
];

// Sample events for each client
export const events: Record<string, EventType[]> = {
  '1': [
    {
      id: uuidv4(),
      caseNumber: 'CC-2022-CR-2275',
      clientName: 'JT; <PERSON><PERSON> (DWI 2nd-Lubbock)',
      description: 'CD client MUST appear',
      date: '07/04/2025',
      startTime: '10:00 am',
      endTime: '12:00 pm',
      isCompleted: false,
    },
    {
      id: uuidv4(),
      caseNumber: 'CC-2022-CR-2275',
      clientName: 'JT; <PERSON><PERSON> (DWI 2nd-Lubbock)',
      description: 'CD client MUST appear',
      date: '10/04/2025',
      startTime: '9:00 am',
      endTime: '10:00 pm',
      isCompleted: true,
    },
  ],
  '2': [
    {
      id: uuidv4(),
      caseNumber: 'CC-2022-CR-2275',
      clientName: 'JT; Amanda Burke (DWI 2nd-Lubbock)',
      description: 'CD client MUST appear',
      date: '07/04/2025',
      startTime: '10:00 am',
      endTime: '12:00 pm',
      isCompleted: true,
    },
  ],
};

export default {
  clients,
  events,
};
