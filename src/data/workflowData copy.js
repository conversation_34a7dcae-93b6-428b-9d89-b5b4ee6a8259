/**
 * Sample workflow data structure for the Workflow component
 * This would typically come from an API or database
 */
const workflowData = {
  elements: [
    {
      tagName: 'main',
      description: 'Main content area containing the form section',
      attributes: { class: 'p-6 max-w-5xl mx-auto' },
      children: [
        {
          tagName: 'section',
          description: 'Section dedicated to an advanced interactive form',
          attributes: { class: 'mb-8', id: 'form-section' },
          children: [
            {
              tagName: 'h3',
              description: 'Form section heading with accessibility',
              attributes: {
                class: 'text-xl font-medium text-gray-700 mb-4',
                id: 'form-heading',
                'aria-label': 'Interactive Form Section',
              },
              content: 'Advanced Interactive Form',
            },
            {
              tagName: 'form',
              description: 'A feature-rich form with diverse input types and validation',
              attributes: {
                class: 'space-y-6',
                method: 'post',
                action: '#',
                onSubmit: "event.preventDefault(); alert('Form submitted successfully!');",
                novalidate: 'false',
                'aria-labelledby': 'form-heading',
              },
              children: [
                {
                  tagName: 'fieldset',
                  description: 'Grouped personal info fields',
                  attributes: { class: 'border p-4 rounded-lg' },
                  children: [
                    {
                      tagName: 'legend',
                      description: 'Title for the personal info fieldset',
                      attributes: { class: 'text-lg font-semibold text-gray-800' },
                      content: 'Personal Information',
                    },
                    {
                      tagName: 'label',
                      description: 'Label for text input',
                      attributes: {
                        for: 'name-input',
                        class: 'block text-gray-700 font-medium mb-1',
                      },
                      content: 'Full Name',
                    },
                    {
                      tagName: 'input',
                      description: 'Text input for name with validation',
                      attributes: {
                        type: 'text',
                        id: 'name-input',
                        name: 'fullName',
                        placeholder: 'Enter your full name',
                        class: 'border p-2 rounded w-full',
                        required: 'true',
                        minlength: '2',
                        maxlength: '50',
                        pattern: '[A-Za-z ]+',
                        title: 'Name must contain only letters and spaces',
                        'aria-describedby': 'name-error',
                      },
                    },
                    {
                      tagName: 'span',
                      description: 'Error message for name input',
                      attributes: {
                        id: 'name-error',
                        class: 'text-red-500 text-sm hidden',
                      },
                      content: 'Please enter a valid name.',
                    },
                    {
                      tagName: 'label',
                      description: 'Label for email input',
                      attributes: {
                        for: 'email-input',
                        class: 'block text-gray-700 font-medium mb-1 mt-4',
                      },
                      content: 'Email Address',
                    },
                    {
                      tagName: 'input',
                      description: 'Email input with validation',
                      attributes: {
                        type: 'email',
                        id: 'email-input',
                        name: 'email',
                        placeholder: 'Enter your email',
                        class: 'border p-2 rounded w-full',
                        required: 'true',
                        'aria-describedby': 'email-error',
                      },
                    },
                    {
                      tagName: 'span',
                      description: 'Error message for email input',
                      attributes: {
                        id: 'email-error',
                        class: 'text-red-500 text-sm hidden',
                      },
                      content: 'Please enter a valid email address.',
                    },
                  ],
                },
                {
                  tagName: 'fieldset',
                  description: 'Grouped preferences fields',
                  attributes: { class: 'border p-4 rounded-lg mt-4' },
                  children: [
                    {
                      tagName: 'legend',
                      description: 'Title for the preferences fieldset',
                      attributes: { class: 'text-lg font-semibold text-gray-800' },
                      content: 'Preferences',
                    },
                    {
                      tagName: 'label',
                      description: 'Label for dropdown',
                      attributes: {
                        for: 'subscription',
                        class: 'block text-gray-700 font-medium mb-1',
                      },
                      content: 'Subscription Plan',
                    },
                    {
                      tagName: 'select',
                      description: 'Dropdown menu with multiple options',
                      attributes: {
                        id: 'subscription',
                        name: 'subscription',
                        class: 'border p-2 rounded w-full',
                        required: 'true',
                      },
                      children: [
                        {
                          tagName: 'option',
                          attributes: { value: '', disabled: 'true', selected: 'true' },
                          content: 'Choose a plan',
                        },
                        {
                          tagName: 'option',
                          attributes: { value: 'basic' },
                          content: 'Basic - $10/month',
                        },
                        {
                          tagName: 'option',
                          attributes: { value: 'premium' },
                          content: 'Premium - $25/month',
                        },
                        {
                          tagName: 'option',
                          attributes: { value: 'enterprise' },
                          content: 'Enterprise - $50/month',
                        },
                      ],
                    },
                    {
                      tagName: 'label',
                      description: 'Label for radio group',
                      attributes: { class: 'block text-gray-700 font-medium mt-4 mb-1' },
                      content: 'Preferred Contact Method',
                    },
                    {
                      tagName: 'div',
                      description: 'Container for radio buttons',
                      attributes: { class: 'flex space-x-4' },
                      children: [
                        {
                          tagName: 'input',
                          description: 'Radio button for email',
                          attributes: {
                            type: 'radio',
                            id: 'contact-email',
                            name: 'contact',
                            value: 'email',
                            class: 'mr-2',
                            required: 'true',
                          },
                        },
                        {
                          tagName: 'label',
                          attributes: { for: 'contact-email', class: 'text-gray-700' },
                          content: 'Email',
                        },
                        {
                          tagName: 'input',
                          description: 'Radio button for phone',
                          attributes: {
                            type: 'radio',
                            id: 'contact-phone',
                            name: 'contact',
                            value: 'phone',
                            class: 'mr-2',
                          },
                        },
                        {
                          tagName: 'label',
                          attributes: { for: 'contact-phone', class: 'text-gray-700' },
                          content: 'Phone',
                        },
                      ],
                    },
                    {
                      tagName: 'label',
                      description: 'Label for checkbox group',
                      attributes: { class: 'block text-gray-700 font-medium mt-4 mb-1' },
                      content: 'Interests',
                    },
                    {
                      tagName: 'div',
                      description: 'Container for checkboxes',
                      attributes: { class: 'flex space-x-4' },
                      children: [
                        {
                          tagName: 'input',
                          description: 'Checkbox for tech',
                          attributes: {
                            type: 'checkbox',
                            id: 'interest-tech',
                            name: 'interests',
                            value: 'tech',
                            class: 'mr-2',
                          },
                        },
                        {
                          tagName: 'label',
                          attributes: { for: 'interest-tech', class: 'text-gray-700' },
                          content: 'Technology',
                        },
                        {
                          tagName: 'input',
                          description: 'Checkbox for art',
                          attributes: {
                            type: 'checkbox',
                            id: 'interest-art',
                            name: 'interests',
                            value: 'art',
                            class: 'mr-2',
                          },
                        },
                        {
                          tagName: 'label',
                          attributes: { for: 'interest-art', class: 'text-gray-700' },
                          content: 'Art',
                        },
                      ],
                    },
                  ],
                },
                {
                  tagName: 'label',
                  description: 'Label for textarea',
                  attributes: {
                    for: 'message',
                    class: 'block text-gray-700 font-medium mb-1',
                  },
                  content: 'Your Message',
                },
                {
                  tagName: 'textarea',
                  description: 'Text area with character limit',
                  attributes: {
                    id: 'message',
                    name: 'message',
                    placeholder: 'Type your message here (max 200 characters)...',
                    class: 'border p-2 rounded w-full',
                    rows: '4',
                    maxlength: '200',
                    required: 'true',
                    'aria-describedby': 'message-error',
                  },
                },
                {
                  tagName: 'span',
                  description: 'Error message for textarea',
                  attributes: {
                    id: 'message-error',
                    class: 'text-red-500 text-sm hidden',
                  },
                  content: 'Message is required.',
                },
                {
                  tagName: 'input',
                  description: 'Number input for age',
                  attributes: {
                    type: 'number',
                    id: 'age',
                    name: 'age',
                    placeholder: 'Enter your age',
                    class: 'border p-2 rounded w-full mt-4',
                    min: '18',
                    max: '100',
                    required: 'true',
                  },
                },
                {
                  tagName: 'input',
                  description: 'Date input for birthdate',
                  attributes: {
                    type: 'date',
                    id: 'birthdate',
                    name: 'birthdate',
                    class: 'border p-2 rounded w-full mt-4',
                    required: 'true',
                  },
                },
                {
                  tagName: 'input',
                  description: 'File input for uploading a profile picture',
                  attributes: {
                    type: 'file',
                    id: 'profile-pic',
                    name: 'profilePic',
                    accept: 'image/*',
                    class: 'border p-2 rounded w-full mt-4',
                  },
                },
                {
                  tagName: 'div',
                  description: 'Container for form buttons',
                  attributes: { class: 'flex space-x-4 mt-6' },
                  children: [
                    {
                      tagName: 'button',
                      description: 'Submit button with hover effect',
                      attributes: {
                        type: 'submit',
                        class:
                          'px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition',
                      },
                      content: 'Submit',
                    },
                    {
                      tagName: 'button',
                      description: 'Reset button with hover effect',
                      attributes: {
                        type: 'reset',
                        class:
                          'px-6 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition',
                        onClick: "alert('Form reset!')",
                      },
                      content: 'Reset',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

export default workflowData;
