/**
 * Format a date using Intl.DateTimeFormat
 * @param date Date to format
 * @param options Intl.DateTimeFormatOptions
 * @param locale Locale string
 * @returns Formatted date string
 */
export const formatDate = (
  date: Date | number,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  },
  locale = 'en-US'
): string => {
  return new Intl.DateTimeFormat(locale, options).format(date);
};

/**
 * Format a number as currency
 * @param amount Amount to format
 * @param currencyCode Currency code (default: USD)
 * @param locale Locale string (default: en-US)
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currencyCode = 'USD', locale = 'en-US'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyCode,
  }).format(amount);
};

/**
 * Truncate a string with ellipsis
 * @param str String to truncate
 * @param maxLength Maximum length
 * @returns Truncated string
 */
export const truncateString = (str: string, maxLength: number): string => {
  if (str.length <= maxLength) return str;
  return `${str.slice(0, maxLength)}...`;
};
