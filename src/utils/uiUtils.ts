/**
 * Get the appropriate CSS class based on workflow status
 * @param status - Workflow status string
 * @returns CSS class string
 */
export const getStatusColorClass = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'running':
      return 'bg-[#8CF1BD] text-[#2A2E34]';
    case 'due soon':
      return 'bg-[#FFDB93] text-[#2A2E34]';
    case 'overdue':
      return 'bg-[#EF8B8B] text-[#2A2E34]';
    case 'completed':
      return 'bg-[#97C7FF] text-[#2A2E34]';
    case 'Archived':
      return 'bg-[#97C7FF] text-[#2A2E34]';
    default:
      return 'bg-gray-100 text-[#2A2E34]';
  }
};
