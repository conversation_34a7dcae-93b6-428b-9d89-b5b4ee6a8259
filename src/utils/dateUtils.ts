/**
 * Format a date string to MM/DD/YY format
 * @param dateString - ISO date string to format
 * @returns Formatted date string
 */
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: '2-digit',
    day: '2-digit',
    year: '2-digit',
  });
};

/**
 * Format a date as a relative time (hours ago or days ago)
 * @param dateString - ISO date string to format
 * @returns Formatted relative time string
 */
export const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);

  if (diffInHours < 24) {
    return `${Math.round(diffInHours)} Hours ago`;
  } else {
    return `${Math.round(diffInHours / 24)} days ago`;
  }
};
