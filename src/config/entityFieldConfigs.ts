/**
 * Entity Field Configurations
 * Defines how to display information for different entity types
 */

import { Contact } from '@/types/contact';
import { Client } from '@/types/client';

interface FieldConfig {
  id: string;
  label: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getValue: (data: any) => string;
  isLink?: boolean;
  colSpan?: number;
}

export const contactFieldConfig: FieldConfig[] = [
  {
    id: 'name',
    label: 'Name',
    getValue: (contact: Contact) => contact.name,
  },
  {
    id: 'mobile',
    label: 'Mobile',
    getValue: (contact: Contact) => contact.mobile || '',
  },
  {
    id: 'home',
    label: 'Home',
    getValue: (contact: Contact) => 
      contact.address?.street && contact.address?.city 
        ? `${contact.address.street}, ${contact.address.city}` 
        : '',
  },
  {
    id: 'work',
    label: 'Work',
    getValue: (contact: Contact) => contact.company || '',
  },
  {
    id: 'email',
    label: 'Email',
    getValue: (contact: Contact) => contact.email,
    isLink: true,
  },
  {
    id: 'description',
    label: 'Description',
    getValue: (contact: Contact) => 
      contact.address?.street && contact.address?.city && contact.address?.state && contact.address?.zipCode
        ? `${contact.address.street} ${contact.address.city}, ${contact.address.state} ${contact.address.zipCode}`
        : contact.notes || '',
    colSpan: 3,
  },
];

export const clientFieldConfig: FieldConfig[] = [
  {
    id: 'name',
    label: 'Name',
    getValue: (client: Client) => client.name,
  },
  {
    id: 'mobile',
    label: 'Mobile',
    getValue: (client: Client) => client.mobile || '',
  },
  {
    id: 'company',
    label: 'Company',
    getValue: (client: Client) => client.company || '',
  },
  {
    id: 'attorney',
    label: 'Attorney',
    getValue: (client: Client) => client.attorney || '',
  },
  {
    id: 'email',
    label: 'Email',
    getValue: (client: Client) => client.email,
    isLink: true,
  },
  {
    id: 'notes',
    label: 'Notes',
    getValue: (client: Client) => client.notes || '',
    colSpan: 3,
  },
];

export type { FieldConfig }; 