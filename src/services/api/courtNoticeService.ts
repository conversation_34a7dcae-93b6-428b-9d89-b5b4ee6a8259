import apiClient from './config';
import { AxiosResponse } from 'axios';
import {
  CourtNoticeListResponse,
  CourtNoticeListParams,
  convertStatusArrayToApiFormat,
} from '@/types/courtNotice';

/**
 * Court Notice API service
 * Handles all court notice related API operations
 */
const courtNoticeService = {
  /**
   * Get court notice list
   * @param params - Payload for the POST request (userId will be automatically added from localStorage)
   * @returns Promise with court notice data
   */
  getCourtNoticeList: async (
    params: Omit<CourtNoticeListParams, 'userId'>
  ): Promise<CourtNoticeListResponse> => {
    try {
      // Get userId from localStorage
      const user = localStorage.getItem('user');
      const userData = user ? JSON.parse(user) : null;
      const userId = userData?.id;

      if (!userId) {
        throw new Error('User ID not found in localStorage');
      }

      // Create POST payload
      const payload: CourtNoticeListParams = {
        ...params,
        userId,
      };

      // Convert sortBy to sort_by and sortOrder to sort_order for API compatibility
      if (params.sortBy) {
        payload.sort_by = params.sortBy;
        delete payload.sortBy;
      }

      if (params.sortOrder) {
        payload.sort_order = params.sortOrder;
        delete payload.sortOrder;
      }

      // Convert status values to API format (with underscores) in filters
      if (payload.filter && payload.filter.length > 0) {
        payload.filter = payload.filter.map(filter => {
          if (
            filter.fieldName?.toLowerCase() === 'status' &&
            filter.value &&
            filter.value.length > 0
          ) {
            return {
              ...filter,
              value: convertStatusArrayToApiFormat(filter.value),
            };
          }
          return filter;
        });
      }

      const response: AxiosResponse<CourtNoticeListResponse> = await apiClient.post(
        '/workflow/court-notice-list',
        payload
      );

      return response.data;
    } catch (error) {
      console.error('CourtNotice API Error:', error);
      throw error;
    }
  },

  /**
   * Get court notice details by ID
   * @param id - Court notice ID
   * @returns Promise with court notice details
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getCourtNoticeById: async (id: string): Promise<any> => {
    const response = await apiClient.get(`/workflow/court-notice/${id}`);
    return response.data;
  },
};

export default courtNoticeService;
