import axios, { AxiosRequestConfig } from 'axios';
import apiClient, { baseURL } from './config';

/**
 * API utilities to centralize all requests and avoid repeating baseURL
 */

// Returns the full URL with baseURL prepended
export const getFullUrl = (endpoint: string): string => {
  // Remove leading slash if present to avoid double slashes
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${baseURL}/${formattedEndpoint}`;
};

// Generic API request methods using apiClient (with auth token)
export const apiGet = <T>(endpoint: string, config?: AxiosRequestConfig) => {
  return apiClient.get<T>(endpoint, config);
};

export const apiPost = <T, D = Record<string, unknown>>(
  endpoint: string,
  data?: D,
  config?: AxiosRequestConfig
) => {
  return apiClient.post<T>(endpoint, data, config);
};

export const apiPut = <T, D = Record<string, unknown>>(
  endpoint: string,
  data?: D,
  config?: AxiosRequestConfig
) => {
  return apiClient.put<T>(endpoint, data, config);
};

export const apiDelete = <T>(endpoint: string, config?: AxiosRequestConfig) => {
  return apiClient.delete<T>(endpoint, config);
};

// For cases where direct axios use is needed (without auth token)
export const publicGet = <T>(endpoint: string, config?: AxiosRequestConfig) => {
  return axios.get<T>(getFullUrl(endpoint), config);
};

export const publicPost = <T, D = Record<string, unknown>>(
  endpoint: string,
  data?: D,
  config?: AxiosRequestConfig
) => {
  return axios.post<T>(getFullUrl(endpoint), data, config);
};

export const publicPut = <T, D = Record<string, unknown>>(
  endpoint: string,
  data?: D,
  config?: AxiosRequestConfig
) => {
  return axios.put<T>(getFullUrl(endpoint), data, config);
};

export const publicDelete = <T>(endpoint: string, config?: AxiosRequestConfig) => {
  return axios.delete<T>(getFullUrl(endpoint), config);
};
