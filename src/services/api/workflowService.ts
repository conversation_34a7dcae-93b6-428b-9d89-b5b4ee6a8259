import { apiGet, apiPost } from './apiUtils';

import { WorkflowResponse } from '@/types/workflow';

// Static response data for development/testing - now using actual API response
/*
const responseDATA =
{
  "fields": [
    {
      id: '68a47027a3cf61b7484737d9',
      template_name: '<PERSON><PERSON>, SA',
      start_date: '08/19/2025',
      end_date: '08/20/2025',
      last_activity: '1 minute ago',
      status: 'ON_TRACK',
      assigned_users: '<PERSON>, <PERSON>',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: '<PERSON>',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter:
        '<PERSON> (DWI-Lubbock) 22-CCR-8000 AA CAP3, <PERSON> (DWI-Lubbock) 22-CCR-8000 AA CAP3',
      follow_up: false,
      attorney: '<PERSON>, <PERSON>',
      _id: '68a47027a3cf61b7484737d9',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a46fa5a3cf61b748473633',
      template_name: '<PERSON><PERSON>, SA',
      start_date: '08/19/2025',
      end_date: '08/20/2025',
      last_activity: '3 minutes ago',
      status: 'ON_TRACK',
      assigned_users: '<PERSON> <PERSON>, <PERSON>',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: '<PERSON> <PERSON>',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter:
        'Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3, Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a46fa5a3cf61b748473633',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a46e56a3cf61b74847349d',
      template_name: 'Jeffery Price, SA',
      start_date: '08/19/2025',
      end_date: '08/20/2025',
      last_activity: '9 minutes ago',
      status: 'ON_TRACK',
      assigned_users: 'Alex Morgan, Riley Brooks',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Joe Clark',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter:
        'Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3, Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a46e56a3cf61b74847349d',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a46e1aa3cf61b748473325',
      template_name: 'Jeffery Price, SA',
      start_date: '08/19/2025',
      end_date: '08/20/2025',
      last_activity: '10 minutes ago',
      status: 'ON_TRACK',
      assigned_users: 'Jamie Taylor, Alex Morgan',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Joe Clark',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter:
        'Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3, Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a46e1aa3cf61b748473325',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a45e6aa3cf61b748473077',
      template_name: 'Jeffery Price, SA',
      start_date: '08/19/2025',
      end_date: '08/20/2025',
      last_activity: '11 minutes ago',
      status: 'ON_TRACK',
      assigned_users: 'Riley Brooks, Jamie Taylor',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Joe Clark',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter:
        'Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3, Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a45e6aa3cf61b748473077',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a45e52a3cf61b74847305c',
      template_name: 'Jeffery Price, SA',
      start_date: '08/19/2025',
      end_date: '08/20/2025',
      last_activity: '1 hour ago',
      status: 'ON_TRACK',
      assigned_users: '',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'New Court Notice',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: '',
      follow_up: false,
      attorney: '',
      _id: '68a45e52a3cf61b74847305c',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a32c14d844edc69c1b8712',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 hour ago',
      status: 'DUE_SOON',
      assigned_users: 'Jamie Taylor',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Smiths Green',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: 'Smith Green (DWI-Lubbock) 2025-CV-045678 CAP5',
      follow_up: false,
      attorney: 'Lisa Spencer',
      _id: '68a32c14d844edc69c1b8712',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a44063a3cf61b748472f93',
      template_name: 'Jeffery Price, SA',
      start_date: '08/19/2025',
      end_date: '08/20/2025',
      last_activity: '3 hours ago',
      status: 'ON_TRACK',
      assigned_users: '',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'New Court Notice',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: '',
      follow_up: false,
      attorney: '',
      _id: '68a44063a3cf61b748472f93',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a32bd8d844edc69c1b8673',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '23 hours ago',
      status: 'DUE_SOON',
      assigned_users: 'Alex Morgan',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'JOE LEAL',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: 'Joe Leal - DWI - CC-2024-CR-2229',
      follow_up: false,
      attorney: 'Lisa Spencer',
      _id: '68a32bd8d844edc69c1b8673',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a327add844edc69c1b822d',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '23 hours ago',
      status: 'DUE_SOON',
      assigned_users: 'Riley Brooks',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Joe Clark',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: 'Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3',
      follow_up: false,
      attorney: 'Lisa Spencer',
      _id: '68a327add844edc69c1b822d',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a3257bd844edc69c1b8170',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '23 hours ago',
      status: 'DUE_SOON',
      assigned_users: 'Jamie Taylor',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'New Court Notice',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: '',
      follow_up: false,
      attorney: '',
      _id: '68a3257bd844edc69c1b8170',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a31b14d844edc69c1b7dcf',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Alex Morgan, Riley Brooks',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Smiths Green',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter:
        'Smith Green (DWI-Lubbock) 2025-CV-045678 CAP5, Smith Green (DWI-Lubbock) 2025-CV-045678 CAP5',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a31b14d844edc69c1b7dcf',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a30e6312eceabd9ee945bb',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Alex Morgan',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Smiths Green',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: 'Smith Green (DWI-Lubbock) 2025-CV-045678 CAP5',
      follow_up: false,
      attorney: 'Lisa Spencer',
      _id: '68a30e6312eceabd9ee945bb',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a30e8712eceabd9ee9463e',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Jamie Taylor',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'New Court Notice',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: '',
      follow_up: false,
      attorney: '',
      _id: '68a30e8712eceabd9ee9463e',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a30a9212eceabd9ee94598',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: '',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'New Court Notice',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: '',
      follow_up: false,
      attorney: '',
      _id: '68a30a9212eceabd9ee94598',
      last_task_id: '6821c2091365677925387046',
      isChild: 'New Court Notice',
      completed_tasks: '0/2',
    },
    {
      id: '68a308a012eceabd9ee94188',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Jamie Taylor, Riley Brooks',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Smiths Green',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter:
        'Smith Green (DWI-Lubbock) 2025-CV-045678 CAP5, Smith Green (DWI-Lubbock) 2025-CV-045678 CAP5',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a308a012eceabd9ee94188',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a2c94ec8ea8a7a5e3edc8f',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Jamie Taylor, Alex Morgan',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'JOE LEAL',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: 'Joe Leal - DWI - CC-2024-CR-2229, Joe Leal - DWI - CC-2024-CR-2229',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a2c94ec8ea8a7a5e3edc8f',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a2c4e1c8ea8a7a5e3ed528',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Alex Morgan, Riley Brooks',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'New Court Notice',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: '',
      follow_up: false,
      attorney: '',
      _id: '68a2c4e1c8ea8a7a5e3ed528',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a2c247c8ea8a7a5e3ec4e6',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Alex Morgan, Riley Brooks',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'Joe Clark',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: 'Joe Clark (DWI-Lubbock) 22-CCR-8000 AA CAP3',
      follow_up: false,
      attorney: 'Lisa Spencer',
      _id: '68a2c247c8ea8a7a5e3ec4e6',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
    {
      id: '68a2c208c8ea8a7a5e3ec230',
      template_name: 'Jeffery Price, SA',
      start_date: '08/18/2025',
      end_date: '08/19/2025',
      last_activity: '1 day ago',
      status: 'OVERDUE',
      assigned_users: 'Jamie Taylor, Alex Morgan',
      assign_by: '',
      run_by: '',
      notes: 'Auto-generated execution',
      work_flow_runner: 'JOE LEAL',
      client_name: '',
      matter_name: '',
      matter_id: '',
      matter: 'Joe Leal - DWI - CC-2024-CR-2229, Joe Leal - DWI - CC-2024-CR-2229',
      follow_up: false,
      attorney: 'Lisa Spencer, Lisa Spencer',
      _id: '68a2c208c8ea8a7a5e3ec230',
      last_task_id: '6821c55d136567792538704c',
      isChild: 'New Court Notice',
      completed_tasks: '1/2',
    },
  ],
  column: [
    {
      FieldName: ' NAME',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'VIEW',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'STARTED',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'DUE ON',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'TASK COMPLETED',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'STATUS',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'ACTIVITY',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'ASSIGNEE',
      searchable: true,
      sortable: true,
      visible: true,
    },
    {
      FieldName: 'MATTER',
      searchable: true,
      sortable: true,
      visible: false,
    },
    {
      FieldName: 'ATTORNEY',
      searchable: true,
      sortable: true,
      visible: false,
    },
  ],
  filter: [
    {
      fieldName: 'work_flow_name',
      filter: 'equal',
      value: ['1', '2', '3'],
    },
    {
      fieldName: 'assign',
      filter: 'equal',
      value: ['1', '2', '3'],
    },
    {
      fieldName: 'status',
      filter: 'equal',
      value: ['completed', 'on_track'],
    },
    {
      fieldName: 'contact',
      filter: 'equal',
      value: ['1', '2', '3'],
    },
    {
      fieldName: 'matter',
      filter: 'equal',
      value: ['1', '2', '3'],
    },
    {
      fieldName: 'template',
      filter: 'equal',
      value: ['1', '2', '3'],
    },
    {
      fieldName: 'due_date',
      filter: 'before',
      value: ['2025-08-25'],
    },
    {
      fieldName: 'created_at',
      filter: 'before',
      value: ['2025-08-25'],
    },
    {
      fieldName: 'attorney',
      filter: 'contain',
      value: ['1', '2', '3'],
    },
  ],
  "total": 306,
  "page": 1,
  "limit": 20
}
*/

/**
 * Workflow API service
 * Handles all workflow-related API operations
 */
const workflowService = {
  /**
   * Get workflows assigned to the current user
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns Promise with workflow data
   */
  getMyWorkflows: async (page: number = 1, limit: number = 10): Promise<WorkflowResponse> => {
    const response = await apiGet<WorkflowResponse>(
      `/workflow/my-workflow?page=${page}&limit=${limit}`
    );
    return response.data;
  },

  workFlowData: async (
    page: number = 1,
    limit: number = 10,
    userId?: string,
    view?: string,
    type?: string
  ): Promise<WorkflowResponse> => {
    const response = await apiPost<WorkflowResponse>(`/workflow/court-notice-list`, {
      page,
      limit,
      userId,
      view,
      type,
    });

    return response.data;
  },
  /**
   * Get a specific workflow by ID
   * @param id - Workflow ID
   * @returns Promise with workflow data
   */
  getWorkflowById: async (id: string) => {
    const response = await apiGet(`/workflow/${id}`);
    return response.data;
  },
};

export default workflowService;
