import axios from 'axios';

/**
 * Base API configuration
 * Uses environment variables for base URL configuration
 */
const baseURL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

const apiClient = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  config => {
    // Try both token keys for compatibility
    const authToken = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
    const selectedToken = authToken || token;

    if (selectedToken) {
      try {
        // Try to parse as JSON first (if stored as JSON string)
        const parsedToken = JSON.parse(selectedToken);
        config.headers.Authorization = `Bearer ${parsedToken}`;
      } catch {
        // If parsing fails, use the token as is (remove quotes if present)
        const cleanToken = selectedToken.replace(/^"|"$/g, '');
        config.headers.Authorization = `Bearer ${cleanToken}`;
      }
    }

    return config;
  },
  error => Promise.reject(error)
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  response => response,
  error => {
    // Handle global error cases like 401 unauthorized
    if (error.response && error.response.status === 401) {
      // Clear auth data if needed
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        localStorage.removeItem('selectedMatterMyMatterId');
        localStorage.removeItem('SelectedEventMatterId');

        // Redirect to login if not already there
        if (window.location.pathname !== '/signin') {
          window.location.href = '/signin';
        }
      }
    }

    return Promise.reject(error);
  }
);

export { baseURL };
export default apiClient;
