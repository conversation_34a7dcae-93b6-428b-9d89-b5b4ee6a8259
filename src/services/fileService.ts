/**
 * File Service
 * Handles all API operations related to files
 */

import { File, FileListParams, FileListResponse, FileFormData } from '@/types/file';

/**
 * File Service Class
 */
export class FileService {
  /**
   * Get files for a specific contact
   */
  static async getFilesByContact(contactId: string): Promise<File[]> {
    // Mock data for demo - in production, this would be an API call
    const mockFiles: Record<string, File[]> = {
      '1': [ // <PERSON>
        {
          id: '1',
          fileName: 'FM-02-1234-34.pdf',
          documentType: 'PDF',
          createDate: '4/15/2024',
          contactId: '1',
          fileSize: '2.4 MB',
          uploadedBy: '<PERSON>',
          description: 'Legal document for case FM-02-1234-34',
        },
        {
          id: '2',
          fileName: 'Contract_Amendment.docx',
          documentType: 'DOCX',
          createDate: '4/12/2024',
          contactId: '1',
          fileSize: '845 KB',
          uploadedBy: '<PERSON><PERSON>',
          description: 'Contract amendment document',
        },
        {
          id: '3',
          fileName: 'Financial_Statement.xlsx',
          documentType: 'XLSX',
          createDate: '4/10/2024',
          contactId: '1',
          fileSize: '1.2 MB',
          uploadedBy: '<PERSON>',
          description: 'Financial statement for case review',
        },
      ],
      '2': [ // Blake Donovan
        {
          id: '4',
          fileName: 'Corporate_Filing.pdf',
          documentType: 'PDF',
          createDate: '4/08/2024',
          contactId: '2',
          fileSize: '3.1 MB',
          uploadedBy: 'Mark Smith',
          description: 'Corporate filing documents',
        },
        {
          id: '5',
          fileName: 'Board_Minutes.docx',
          documentType: 'DOCX',
          createDate: '4/05/2024',
          contactId: '2',
          fileSize: '1.8 MB',
          uploadedBy: 'Blake Donovan',
          description: 'Board meeting minutes',
        },
      ],
      '5': [ // John Donte
        {
          id: '6',
          fileName: 'Settlement_Agreement.pdf',
          documentType: 'PDF',
          createDate: '3/15/2022',
          contactId: '5',
          fileSize: '2.7 MB',
          uploadedBy: 'K. Melendez',
          description: 'Final settlement agreement',
        },
      ],
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return mockFiles[contactId] || [];
  }

  /**
   * Get a single file by ID
   */
  static async getFile(id: string): Promise<File | null> {
    // For demo purposes - in production, this would be an API call
    const allFiles = await Promise.all([
      this.getFilesByContact('1'),
      this.getFilesByContact('2'),
      this.getFilesByContact('5'),
    ]);
    
    const flatFiles = allFiles.flat();
    return flatFiles.find(file => file.id === id) || null;
  }

  /**
   * Get all files with pagination and filters
   */
  static async getFiles(params: FileListParams = {}): Promise<FileListResponse> {
    // For demo purposes - in production, this would be an API call
    const allFiles = await Promise.all([
      this.getFilesByContact('1'),
      this.getFilesByContact('2'),
      this.getFilesByContact('5'),
    ]);
    
    let filteredFiles = allFiles.flat();
    
    // Apply filters
    if (params.contactId) {
      filteredFiles = filteredFiles.filter(f => f.contactId === params.contactId);
    }
    
    if (params.documentType) {
      filteredFiles = filteredFiles.filter(f => f.documentType === params.documentType);
    }
    
    if (params.search) {
      const searchLower = params.search.toLowerCase();
      filteredFiles = filteredFiles.filter(f => 
        f.fileName.toLowerCase().includes(searchLower) ||
        f.description?.toLowerCase().includes(searchLower)
      );
    }
    
    return {
      data: filteredFiles,
      total: filteredFiles.length,
      page: params.page || 1,
      limit: params.limit || 50,
      totalPages: Math.ceil(filteredFiles.length / (params.limit || 50)),
    };
  }

  /**
   * Upload a new file
   */
  static async uploadFile(fileData: FileFormData, file: globalThis.File): Promise<File> {
    // Mock implementation - in production, this would be an API call
    const newFile: File = {
      id: Date.now().toString(),
      fileName: file.name,
      documentType: fileData.documentType,
      createDate: new Date().toLocaleDateString(),
      contactId: fileData.contactId,
      fileSize: `${(file.size / 1024 / 1024).toFixed(1)} MB`,
      uploadedBy: 'Current User',
      description: fileData.description,
      tags: fileData.tags,
      isPublic: fileData.isPublic,
    };
    
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate upload time
    return newFile;
  }

  /**
   * Update file metadata
   */
  static async updateFile(id: string, fileData: Partial<FileFormData>): Promise<File> {
    // Mock implementation - in production, this would be an API call
    const existingFile = await this.getFile(id);
    if (!existingFile) {
      throw new Error('File not found');
    }
    
    const updatedFile = { ...existingFile, ...fileData };
    await new Promise(resolve => setTimeout(resolve, 200));
    return updatedFile;
  }

  /**
   * Delete a file
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  static async deleteFile(id: string): Promise<void> {
    // Mock implementation - in production, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  /**
   * Download a file
   */
  static async downloadFile(id: string): Promise<Blob> {
    // Mock implementation - in production, this would be an API call
    const file = await this.getFile(id);
    if (!file) {
      throw new Error('File not found');
    }
    
    // Return a mock blob
    await new Promise(resolve => setTimeout(resolve, 500));
    return new Blob(['Mock file content'], { type: 'application/pdf' });
  }
}

// Export individual methods for convenience
export const {
  getFilesByContact,
  getFile,
  getFiles,
  uploadFile,
  updateFile,
  deleteFile,
  downloadFile,
} = FileService; 