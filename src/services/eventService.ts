/**
 * Event Service
 * Handles all API operations related to events
 */

import { Event, EventListParams, EventListResponse } from '@/types/event';

/**
 * Event Service Class
 */
export class EventService {
  /**
   * Get events for a specific contact/client
   */
  static async getEventsByContact(contactId: string): Promise<Event[]> {
    // Mock data for demo - in production, this would be an API call
    const mockEvents: Record<string, Event[]> = {
      '1': [ // <PERSON>
        {
          id: '1',
          eventType: 'Court Notice',
          courtNoticeType: 'Jury Trial',
          attorney: '<PERSON><PERSON>',
          start: '05/21/2024 1:30 pm',
          end: '05/21/2024 2:30 pm',
          location: 'Zoom',
          status: 'Completed',
          contactId: '1',
          matterId: '1',
        },
        {
          id: '2',
          eventType: 'Discovery',
          courtNoticeType: 'Jury Trial',
          attorney: '<PERSON><PERSON> <PERSON>',
          start: '1.5 hrs',
          end: '05/28/2024 3:30 pm',
          location: 'Hackensack',
          status: 'Active',
          contactId: '1',
          matterId: '1',
        },
        {
          id: '3',
          eventType: 'Client meeting',
          courtNoticeType: 'Jury Trial',
          attorney: '<PERSON><PERSON>',
          start: '30 mins',
          end: '06/10/2024 11:30 am',
          location: 'Zoom',
          status: 'Completed',
          contactId: '1',
          matterId: '2',
        },
        {
          id: '4',
          eventType: 'Court Appearance',
          courtNoticeType: 'Jury Trial',
          attorney: 'Debra Lee',
          start: '4 hrs',
          end: '06/15/2024 08:00 am',
          location: 'Country Court',
          status: 'Completed',
          contactId: '1',
          matterId: '2',
        },
      ],
      '2': [ // Blake Donovan
        {
          id: '5',
          eventType: 'Client meeting',
          courtNoticeType: 'Status Conference',
          attorney: 'Mark Smith',
          start: '1 hr',
          end: '06/25/2024 2:00 pm',
          location: 'Office',
          status: 'Active',
          contactId: '2',
          matterId: '3',
        },
        {
          id: '6',
          eventType: 'Court Notice',
          courtNoticeType: 'Motion Hearing',
          attorney: 'Mark Smith',
          start: '07/01/2024 10:00 am',
          end: '07/01/2024 11:30 am',
          location: 'Quebec Superior Court',
          status: 'Pending',
          contactId: '2',
          matterId: '3',
        },
      ],
      '5': [ // John Donte
        {
          id: '7',
          eventType: 'Discovery',
          courtNoticeType: 'Settlement Conference',
          attorney: 'K. Melendez',
          start: '2 hrs',
          end: '03/15/2022 4:00 pm',
          location: 'Office',
          status: 'Completed',
          contactId: '5',
          matterId: '4',
        },
      ],
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return mockEvents[contactId] || [];
  }

  /**
   * Get a single event by ID
   */
  static async getEvent(id: string): Promise<Event | null> {
    // For demo purposes - in production, this would be an API call
    const allEvents = await Promise.all([
      this.getEventsByContact('1'),
      this.getEventsByContact('2'),
      this.getEventsByContact('5'),
    ]);
    
    const flatEvents = allEvents.flat();
    return flatEvents.find(event => event.id === id) || null;
  }

  /**
   * Get all events with pagination and filters
   */
  static async getEvents(params: EventListParams = {}): Promise<EventListResponse> {
    // For demo purposes - in production, this would be an API call
    const allEvents = await Promise.all([
      this.getEventsByContact('1'),
      this.getEventsByContact('2'),
      this.getEventsByContact('5'),
    ]);
    
    const flatEvents = allEvents.flat();
    
    return {
      data: flatEvents,
      total: flatEvents.length,
      page: params.page || 1,
      limit: params.limit || 50,
      totalPages: Math.ceil(flatEvents.length / (params.limit || 50)),
    };
  }

  /**
   * Create a new event
   */
  static async createEvent(eventData: Partial<Event>): Promise<Event> {
    // Mock implementation - in production, this would be an API call
    const newEvent: Event = {
      id: Date.now().toString(),
      eventType: 'Client meeting',
      attorney: '',
      start: '',
      end: '',
      location: '',
      status: 'Pending',
      contactId: '',
      ...eventData,
    } as Event;
    
    await new Promise(resolve => setTimeout(resolve, 200));
    return newEvent;
  }

  /**
   * Update an existing event
   */
  static async updateEvent(id: string, eventData: Partial<Event>): Promise<Event> {
    // Mock implementation - in production, this would be an API call
    const existingEvent = await this.getEvent(id);
    if (!existingEvent) {
      throw new Error('Event not found');
    }
    
    return {
      ...existingEvent,
      ...eventData,
    };
  }

  /**
   * Delete an event
   */
  static async deleteEvent(): Promise<void> {
    // Mock implementation - in production, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// Export individual methods for convenience
export const {
  getEventsByContact,
  getEvent,
  getEvents,
  createEvent,
  updateEvent,
  deleteEvent,
} = EventService; 