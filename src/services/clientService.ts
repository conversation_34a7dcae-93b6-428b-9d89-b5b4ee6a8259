/**
 * Client Service
 * Handles all API operations related to clients
 */

import { Client, ClientListParams, ClientListResponse, ClientFormData } from '@/types/client';

/**
 * Client Service Class
 */
export class ClientService {
  /**
   * Get a single client by ID
   */
  static async getClient(id: string): Promise<Client> {
    // Mock data for demo - in production, this would be an API call
    const mockClients: Client[] = [
      {
        id: '1',
        name: '<PERSON>',
        status: 'Active',
        type: 'Client',
        email: '<EMAIL>',
        mobile: '(*************',
        attorney: '<PERSON>',
        syncStatus: 'Synced',
        notes: 'Important client with multiple ongoing matters. Prefers email communication.',
        company: 'Burke & Associates',
      },
      {
        id: '2',
        name: '<PERSON>',
        status: 'Active',
        type: 'Client',
        email: '<EMAIL>',
        mobile: '(*************',
        attorney: '<PERSON>',
        syncStatus: 'Synced',
        notes: 'Recently moved from Toronto. Contact via mobile.',
        company: 'Donovan Enterprises',
      },
      {
        id: '3',
        name: '<PERSON><PERSON>',
        status: 'Closed',
        type: 'Client',
        email: '<EMAIL>',
        mobile: '(*************',
        attorney: '<PERSON>. <PERSON>',
        syncStatus: 'Synced',
        notes: 'Case closed successfully in 2023. Very satisfied with service.',
        company: 'Clarkson Industries',
      },
      {
        id: '4',
        name: '<PERSON> Donte',
        status: 'Closed',
        type: 'Client',
        email: '<EMAIL>',
        mobile: '(*************',
        attorney: 'K. Melendez',
        syncStatus: 'Synced',
        notes: 'Case completed in 2022. Referred by Amanda Burke.',
        company: 'NYC Consulting',
      },
      {
        id: '5',
        name: 'Lucas Butler',
        status: 'Closed',
        type: 'Client',
        email: '<EMAIL>',
        mobile: '(*************',
        attorney: 'T. Fredrick',
        syncStatus: 'Synced',
        notes: 'Long-time client. Prefers phone calls over email.',
        company: 'Butler Holdings',
      },
    ];

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const client = mockClients.find(c => c.id === id);
    if (!client) {
      throw new Error('Client not found');
    }
    
    return client;
  }

  /**
   * Get list of clients with pagination and filters
   */
  static async getClients(params: ClientListParams = {}): Promise<ClientListResponse> {
    // For demo purposes - in production, this would be an API call
    const allClients = await Promise.all([
      this.getClient('1'),
      this.getClient('2'),
      this.getClient('3'),
      this.getClient('4'),
      this.getClient('5'),
    ]);

    return {
      data: allClients,
      total: allClients.length,
      page: params.page || 1,
      limit: params.limit || 50,
      totalPages: Math.ceil(allClients.length / (params.limit || 50)),
    };
  }

  /**
   * Create a new client
   */
  static async createClient(clientData: ClientFormData): Promise<Client> {
    // Mock implementation - in production, this would be an API call
    const newClient: Client = {
      id: Date.now().toString(),
      name: clientData.name,
      email: clientData.email,
      mobile: clientData.mobile,
      status: clientData.status,
      attorney: clientData.attorney || '',
      notes: clientData.notes,
      company: clientData.company,
      type: 'Client',
      syncStatus: 'Manual',
    };
    
    return newClient;
  }

  /**
   * Update an existing client
   */
  static async updateClient(id: string, clientData: Partial<ClientFormData>): Promise<Client> {
    // Mock implementation - in production, this would be an API call
    const existingClient = await this.getClient(id);
    return {
      ...existingClient,
      ...clientData,
    };
  }

  /**
   * Delete a client
   */
  static async deleteClient(): Promise<void> {
    // Mock implementation - in production, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// Export individual methods for convenience
export const {
  getClient,
  getClients,
  createClient,
  updateClient,
  deleteClient,
} = ClientService; 